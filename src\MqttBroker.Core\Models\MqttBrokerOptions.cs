namespace MqttBroker.Core.Models;

/// <summary>
/// MQTT Broker 配置选项
/// </summary>
public class MqttBrokerOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "MqttBroker";

    /// <summary>
    /// 服务器选项
    /// </summary>
    public ServerOptions ServerOptions { get; set; } = new();

    /// <summary>
    /// 认证选项
    /// </summary>
    public AuthenticationOptions Authentication { get; set; } = new();

    /// <summary>
    /// 持久化选项
    /// </summary>
    public PersistenceOptions Persistence { get; set; } = new();

    /// <summary>
    /// 日志选项
    /// </summary>
    public LoggingOptions Logging { get; set; } = new();
}

/// <summary>
/// 服务器配置选项
/// </summary>
public class ServerOptions
{
    /// <summary>
    /// MQTT端口
    /// </summary>
    public int Port { get; set; } = 1883;

    /// <summary>
    /// MQTT over TLS端口
    /// </summary>
    public int TlsPort { get; set; } = 8883;

    /// <summary>
    /// WebSocket端口
    /// </summary>
    public int WebSocketPort { get; set; } = 8080;

    /// <summary>
    /// 每个客户端最大待处理消息数
    /// </summary>
    public int MaxPendingMessagesPerClient { get; set; } = 250;

    /// <summary>
    /// 默认通信超时时间
    /// </summary>
    public TimeSpan DefaultCommunicationTimeout { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// 是否启用持久会话
    /// </summary>
    public bool EnablePersistentSessions { get; set; } = true;

    /// <summary>
    /// 最大保留消息数
    /// </summary>
    public int MaxRetainedMessages { get; set; } = 5000;

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; set; } = 10000;

    /// <summary>
    /// 心跳检查间隔
    /// </summary>
    public TimeSpan HeartbeatInterval { get; set; } = TimeSpan.FromSeconds(30);
}

/// <summary>
/// 认证配置选项
/// </summary>
public class AuthenticationOptions
{
    /// <summary>
    /// 是否允许匿名连接
    /// </summary>
    public bool AllowAnonymous { get; set; } = false;

    /// <summary>
    /// 是否要求客户端ID
    /// </summary>
    public bool RequireClientId { get; set; } = true;

    /// <summary>
    /// 是否验证客户端ID
    /// </summary>
    public bool ValidateClientId { get; set; } = true;

    /// <summary>
    /// 默认用户名
    /// </summary>
    public string? DefaultUsername { get; set; }

    /// <summary>
    /// 默认密码
    /// </summary>
    public string? DefaultPassword { get; set; }

    /// <summary>
    /// JWT密钥
    /// </summary>
    public string? JwtSecret { get; set; }

    /// <summary>
    /// JWT过期时间
    /// </summary>
    public TimeSpan JwtExpiration { get; set; } = TimeSpan.FromHours(24);
}

/// <summary>
/// 持久化配置选项
/// </summary>
public class PersistenceOptions
{
    /// <summary>
    /// 持久化提供程序
    /// </summary>
    public string Provider { get; set; } = "EntityFramework";

    /// <summary>
    /// 保留消息数量限制
    /// </summary>
    public int RetainedMessagesCount { get; set; } = 1000;

    /// <summary>
    /// QoS消息数量限制
    /// </summary>
    public int QoSMessagesCount { get; set; } = 1000;

    /// <summary>
    /// 数据清理间隔
    /// </summary>
    public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// 消息保留时间
    /// </summary>
    public TimeSpan MessageRetentionTime { get; set; } = TimeSpan.FromDays(7);
}

/// <summary>
/// 日志配置选项
/// </summary>
public class LoggingOptions
{
    /// <summary>
    /// 是否记录客户端连接
    /// </summary>
    public bool LogClientConnections { get; set; } = true;

    /// <summary>
    /// 是否记录发布的消息
    /// </summary>
    public bool LogPublishedMessages { get; set; } = false;

    /// <summary>
    /// 是否记录订阅
    /// </summary>
    public bool LogSubscriptions { get; set; } = true;

    /// <summary>
    /// 是否记录性能指标
    /// </summary>
    public bool LogPerformanceMetrics { get; set; } = true;

    /// <summary>
    /// 日志级别
    /// </summary>
    public string LogLevel { get; set; } = "Information";
}
