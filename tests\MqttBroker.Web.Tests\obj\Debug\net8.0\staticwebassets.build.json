{"Version": 1, "Hash": "dG3bVrvTbgSwMi5mHyhdsN5inbIhxWrVVrzU0Mw4Smk=", "Source": "MqttBroker.Web.Tests", "BasePath": "_content/MqttBroker.Web.Tests", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "D:\\01 Broker\\src\\MqttBroker.Web\\MqttBroker.Web.csproj", "Version": 2, "Source": "MqttBroker.Web", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}