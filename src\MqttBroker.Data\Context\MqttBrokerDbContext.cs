using Microsoft.EntityFrameworkCore;
using MqttBroker.Data.Entities;

namespace MqttBroker.Data.Context;

/// <summary>
/// MQTT Broker 数据库上下文
/// </summary>
public class MqttBrokerDbContext : DbContext
{
    public MqttBrokerDbContext(DbContextOptions<MqttBrokerDbContext> options)
        : base(options)
    {
    }

    /// <summary>
    /// 客户端表
    /// </summary>
    public DbSet<Client> Clients { get; set; } = null!;

    /// <summary>
    /// 会话表
    /// </summary>
    public DbSet<Session> Sessions { get; set; } = null!;

    /// <summary>
    /// 订阅表
    /// </summary>
    public DbSet<Subscription> Subscriptions { get; set; } = null!;

    /// <summary>
    /// 消息表
    /// </summary>
    public DbSet<Message> Messages { get; set; } = null!;

    /// <summary>
    /// 保留消息表
    /// </summary>
    public DbSet<RetainedMessage> RetainedMessages { get; set; } = null!;

    /// <summary>
    /// 配置实体模型
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置Client实体
        modelBuilder.Entity<Client>(entity =>
        {
            entity.HasKey(e => e.ClientId);
            entity.Property(e => e.ClientId).HasMaxLength(128).IsRequired();
            entity.Property(e => e.Username).HasMaxLength(256);
            entity.Property(e => e.Password).HasMaxLength(256);
            entity.Property(e => e.LastSeen).IsRequired();
            entity.Property(e => e.KeepAlive).IsRequired();
            
            entity.HasIndex(e => e.Username);
            entity.HasIndex(e => e.IsConnected);
            entity.HasIndex(e => e.LastSeen);
        });

        // 配置Session实体
        modelBuilder.Entity<Session>(entity =>
        {
            entity.HasKey(e => e.SessionId);
            entity.Property(e => e.SessionId).HasMaxLength(128).IsRequired();
            entity.Property(e => e.ClientId).HasMaxLength(128).IsRequired();
            entity.Property(e => e.CreatedAt).IsRequired();
            entity.Property(e => e.LastActivity).IsRequired();
            
            entity.HasOne<Client>()
                  .WithMany()
                  .HasForeignKey(e => e.ClientId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasIndex(e => e.ClientId);
            entity.HasIndex(e => e.IsPersistent);
            entity.HasIndex(e => e.LastActivity);
        });

        // 配置Subscription实体
        modelBuilder.Entity<Subscription>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ClientId).HasMaxLength(128).IsRequired();
            entity.Property(e => e.TopicFilter).HasMaxLength(512).IsRequired();
            entity.Property(e => e.QoS).IsRequired();
            entity.Property(e => e.CreatedAt).IsRequired();
            
            entity.HasOne<Client>()
                  .WithMany()
                  .HasForeignKey(e => e.ClientId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasIndex(e => e.ClientId);
            entity.HasIndex(e => e.TopicFilter);
            entity.HasIndex(e => new { e.ClientId, e.TopicFilter }).IsUnique();
        });

        // 配置Message实体
        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Topic).HasMaxLength(512).IsRequired();
            entity.Property(e => e.Payload).IsRequired();
            entity.Property(e => e.QoS).IsRequired();
            entity.Property(e => e.Timestamp).IsRequired();
            
            entity.HasIndex(e => e.Topic);
            entity.HasIndex(e => e.Timestamp);
            entity.HasIndex(e => e.QoS);
            entity.HasIndex(e => e.Retain);
        });

        // 配置RetainedMessage实体
        modelBuilder.Entity<RetainedMessage>(entity =>
        {
            entity.HasKey(e => e.Topic);
            entity.Property(e => e.Topic).HasMaxLength(512).IsRequired();
            entity.Property(e => e.Payload).IsRequired();
            entity.Property(e => e.QoS).IsRequired();
            entity.Property(e => e.Timestamp).IsRequired();
            
            entity.HasIndex(e => e.Timestamp);
            entity.HasIndex(e => e.QoS);
        });
    }

    /// <summary>
    /// 配置数据库连接
    /// </summary>
    /// <param name="optionsBuilder">选项构建器</param>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // 默认使用SQLite（用于开发和测试）
            optionsBuilder.UseSqlite("Data Source=mqttbroker.db");
        }
    }
}
