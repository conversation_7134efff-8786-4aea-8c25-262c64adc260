{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "System": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=MqttBrokerDb;Trusted_Connection=true;MultipleActiveResultSets=true", "SqliteConnection": "Data Source=mqttbroker.db"}, "MqttBroker": {"ServerOptions": {"Port": 1883, "TlsPort": 8883, "WebSocketPort": 8080, "MaxPendingMessagesPerClient": 250, "DefaultCommunicationTimeout": "00:01:00", "EnablePersistentSessions": true, "MaxRetainedMessages": 5000}, "Authentication": {"AllowAnonymous": true, "RequireClientId": false, "ValidateClientId": false}, "Persistence": {"Provider": "Sqlite", "RetainedMessagesCount": 1000, "QoSMessagesCount": 1000}, "Logging": {"LogClientConnections": true, "LogPublishedMessages": true, "LogSubscriptions": true}}, "TestClient": {"ServerHost": "localhost", "ServerPort": 1883, "ClientId": "TestClient", "Username": "", "Password": "", "TestTopics": ["test/topic1", "test/topic2", "sensors/temperature", "sensors/humidity"]}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/console-.log", "rollingInterval": "Day", "retainedFileCountLimit": 3, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}