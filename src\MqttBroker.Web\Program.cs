using MqttBroker.Core.Services;
using MqttBroker.Data.Context;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MqttBroker.Web;

/// <summary>
/// MQTT Broker Web应用程序入口点
/// </summary>
public class Program
{
    public static void Main(string[] args)
    {
        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(GetConfiguration())
            .CreateLogger();

        try
        {
            Log.Information("启动 MQTT Broker Web 应用程序");
            
            var builder = WebApplication.CreateBuilder(args);
            
            // 配置服务
            ConfigureServices(builder);
            
            var app = builder.Build();
            
            // 配置中间件管道
            ConfigureMiddleware(app);
            
            // 确保数据库已创建
            EnsureDatabaseCreated(app);
            
            app.Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// 配置服务
    /// </summary>
    private static void ConfigureServices(WebApplicationBuilder builder)
    {
        // 添加Serilog
        builder.Host.UseSerilog();

        // 添加控制器
        builder.Services.AddControllers();
        
        // 添加API文档
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen();
        
        // 添加SignalR
        builder.Services.AddSignalR();
        
        // WebSocket支持已内置在ASP.NET Core中
        
        // 添加数据库上下文
        var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
        builder.Services.AddDbContext<MqttBrokerDbContext>(options =>
            options.UseSqlServer(connectionString));
        
        // 添加MQTT Broker核心服务
        // TODO: 在后续开发中添加具体的服务注册
        
        // 添加CORS
        builder.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                policy.AllowAnyOrigin()
                      .AllowAnyMethod()
                      .AllowAnyHeader();
            });
        });
    }

    /// <summary>
    /// 配置中间件管道
    /// </summary>
    private static void ConfigureMiddleware(WebApplication app)
    {
        // 开发环境配置
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        // 启用HTTPS重定向
        app.UseHttpsRedirection();
        
        // 启用CORS
        app.UseCors();
        
        // 启用WebSocket
        app.UseWebSockets();
        
        // 启用路由
        app.UseRouting();
        
        // 启用授权
        app.UseAuthorization();
        
        // 映射控制器
        app.MapControllers();
        
        // 映射SignalR Hub
        // TODO: 在后续开发中添加具体的Hub映射
        
        // 健康检查端点
        app.MapGet("/health", () => Results.Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow }));
    }

    /// <summary>
    /// 确保数据库已创建
    /// </summary>
    private static void EnsureDatabaseCreated(WebApplication app)
    {
        using var scope = app.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MqttBrokerDbContext>();
        
        try
        {
            context.Database.EnsureCreated();
            Log.Information("数据库初始化完成");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "数据库初始化失败");
        }
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    private static IConfiguration GetConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();
    }
}
