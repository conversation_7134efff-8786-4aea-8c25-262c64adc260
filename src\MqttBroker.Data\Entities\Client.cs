using System.ComponentModel.DataAnnotations;

namespace MqttBroker.Data.Entities;

/// <summary>
/// MQTT客户端实体
/// </summary>
public class Client
{
    /// <summary>
    /// 客户端ID（主键）
    /// </summary>
    [Key]
    [Required]
    [StringLength(128)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    [StringLength(256)]
    public string? Username { get; set; }

    /// <summary>
    /// 密码（加密存储）
    /// </summary>
    [StringLength(256)]
    public string? Password { get; set; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    public DateTime LastSeen { get; set; }

    /// <summary>
    /// 是否清理会话
    /// </summary>
    public bool CleanSession { get; set; }

    /// <summary>
    /// 心跳间隔（秒）
    /// </summary>
    public int KeepAlive { get; set; }

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    [StringLength(45)] // IPv6最大长度
    public string? IpAddress { get; set; }

    /// <summary>
    /// 客户端端口
    /// </summary>
    public int? Port { get; set; }

    /// <summary>
    /// 协议版本
    /// </summary>
    [StringLength(10)]
    public string? ProtocolVersion { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 连接次数
    /// </summary>
    public int ConnectionCount { get; set; }

    /// <summary>
    /// 最后断开时间
    /// </summary>
    public DateTime? LastDisconnectedAt { get; set; }

    /// <summary>
    /// 断开原因
    /// </summary>
    [StringLength(256)]
    public string? DisconnectReason { get; set; }

    /// <summary>
    /// 是否被禁用
    /// </summary>
    public bool IsDisabled { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    [StringLength(512)]
    public string? Notes { get; set; }
}
