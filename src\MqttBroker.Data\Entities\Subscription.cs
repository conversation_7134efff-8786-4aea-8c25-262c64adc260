using System.ComponentModel.DataAnnotations;

namespace MqttBroker.Data.Entities;

/// <summary>
/// MQTT订阅实体
/// </summary>
public class Subscription
{
    /// <summary>
    /// 订阅ID（主键）
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// 客户端ID（外键）
    /// </summary>
    [Required]
    [StringLength(128)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题过滤器
    /// </summary>
    [Required]
    [StringLength(512)]
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// QoS等级
    /// </summary>
    [Range(0, 2)]
    public byte QoS { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否活跃
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 订阅选项（MQTT 5.0）
    /// </summary>
    public byte SubscriptionOptions { get; set; }

    /// <summary>
    /// 订阅标识符（MQTT 5.0）
    /// </summary>
    public int? SubscriptionIdentifier { get; set; }

    /// <summary>
    /// 匹配的消息数量
    /// </summary>
    public long MatchedMessagesCount { get; set; }

    /// <summary>
    /// 最后匹配时间
    /// </summary>
    public DateTime? LastMatchedAt { get; set; }

    /// <summary>
    /// 订阅属性（MQTT 5.0，JSON格式）
    /// </summary>
    public string? Properties { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    [StringLength(256)]
    public string? Notes { get; set; }
}
