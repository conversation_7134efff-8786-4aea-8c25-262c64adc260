using System.ComponentModel.DataAnnotations;

namespace MqttBroker.Data.Entities;

/// <summary>
/// MQTT会话实体
/// </summary>
public class Session
{
    /// <summary>
    /// 会话ID（主键）
    /// </summary>
    [Key]
    [Required]
    [StringLength(128)]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 客户端ID（外键）
    /// </summary>
    [Required]
    [StringLength(128)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 是否持久会话
    /// </summary>
    public bool IsPersistent { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 会话过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 会话状态
    /// </summary>
    [StringLength(20)]
    public string Status { get; set; } = "Active";

    /// <summary>
    /// 待发送消息数量
    /// </summary>
    public int PendingMessagesCount { get; set; }

    /// <summary>
    /// 订阅数量
    /// </summary>
    public int SubscriptionsCount { get; set; }

    /// <summary>
    /// 会话数据（JSON格式存储额外信息）
    /// </summary>
    public string? SessionData { get; set; }

    /// <summary>
    /// 最大QoS等级
    /// </summary>
    public byte MaxQoS { get; set; } = 2;

    /// <summary>
    /// 是否接收保留消息
    /// </summary>
    public bool ReceiveRetainedMessages { get; set; } = true;

    /// <summary>
    /// 会话属性（MQTT 5.0）
    /// </summary>
    public string? Properties { get; set; }
}
