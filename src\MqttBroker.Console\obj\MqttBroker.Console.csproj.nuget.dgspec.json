{"format": 1, "restore": {"D:\\01 Broker\\src\\MqttBroker.Console\\MqttBroker.Console.csproj": {}}, "projects": {"D:\\01 Broker\\src\\MqttBroker.Console\\MqttBroker.Console.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\src\\MqttBroker.Console\\MqttBroker.Console.csproj", "projectName": "MqttBroker.Con<PERSON>e", "projectPath": "D:\\01 Broker\\src\\MqttBroker.Console\\MqttBroker.Console.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\src\\MqttBroker.Console\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\01 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj": {"projectPath": "D:\\01 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj"}, "D:\\01 Broker\\src\\MqttBroker.Data\\MqttBroker.Data.csproj": {"projectPath": "D:\\01 Broker\\src\\MqttBroker.Data\\MqttBroker.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MQTTnet": {"target": "Package", "version": "[5.0.1.1416, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.6, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\01 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj", "projectName": "MqttBroker.Core", "projectPath": "D:\\01 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\src\\MqttBroker.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.6, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.6, )"}, "System.Threading.Channels": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\01 Broker\\src\\MqttBroker.Data\\MqttBroker.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\src\\MqttBroker.Data\\MqttBroker.Data.csproj", "projectName": "MqttBroker.Data", "projectPath": "D:\\01 Broker\\src\\MqttBroker.Data\\MqttBroker.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\src\\MqttBroker.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\01 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj": {"projectPath": "D:\\01 Broker\\src\\MqttBroker.Core\\MqttBroker.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}