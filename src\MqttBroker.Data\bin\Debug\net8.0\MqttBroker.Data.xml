<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MqttBroker.Data</name>
    </assembly>
    <members>
        <member name="T:MqttBroker.Data.Context.MqttBrokerDbContext">
            <summary>
            MQTT Broker 数据库上下文
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Context.MqttBrokerDbContext.Clients">
            <summary>
            客户端表
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Context.MqttBrokerDbContext.Sessions">
            <summary>
            会话表
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Context.MqttBrokerDbContext.Subscriptions">
            <summary>
            订阅表
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Context.MqttBrokerDbContext.Messages">
            <summary>
            消息表
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Context.MqttBrokerDbContext.RetainedMessages">
            <summary>
            保留消息表
            </summary>
        </member>
        <member name="M:MqttBroker.Data.Context.MqttBrokerDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置实体模型
            </summary>
            <param name="modelBuilder">模型构建器</param>
        </member>
        <member name="M:MqttBroker.Data.Context.MqttBrokerDbContext.OnConfiguring(Microsoft.EntityFrameworkCore.DbContextOptionsBuilder)">
            <summary>
            配置数据库连接
            </summary>
            <param name="optionsBuilder">选项构建器</param>
        </member>
        <member name="T:MqttBroker.Data.Entities.Client">
            <summary>
            MQTT客户端实体
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.ClientId">
            <summary>
            客户端ID（主键）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.Password">
            <summary>
            密码（加密存储）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.IsConnected">
            <summary>
            是否已连接
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.LastSeen">
            <summary>
            最后活跃时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.CleanSession">
            <summary>
            是否清理会话
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.KeepAlive">
            <summary>
            心跳间隔（秒）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.IpAddress">
            <summary>
            客户端IP地址
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.Port">
            <summary>
            客户端端口
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.UpdatedAt">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.ConnectionCount">
            <summary>
            连接次数
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.LastDisconnectedAt">
            <summary>
            最后断开时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.DisconnectReason">
            <summary>
            断开原因
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.IsDisabled">
            <summary>
            是否被禁用
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Client.Notes">
            <summary>
            备注信息
            </summary>
        </member>
        <member name="T:MqttBroker.Data.Entities.Message">
            <summary>
            MQTT消息实体
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Id">
            <summary>
            消息ID（主键）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Topic">
            <summary>
            消息主题
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.QoS">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Timestamp">
            <summary>
            消息时间戳
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.ExpiryInterval">
            <summary>
            消息过期时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.PublisherClientId">
            <summary>
            发布者客户端ID
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.MessageId">
            <summary>
            消息标识符（用于QoS 1和2）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Duplicate">
            <summary>
            重复标志
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Status">
            <summary>
            消息状态
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.LastRetryAt">
            <summary>
            最后重试时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.Properties">
            <summary>
            消息属性（MQTT 5.0，JSON格式）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.ResponseTopic">
            <summary>
            响应主题（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.CorrelationData">
            <summary>
            相关数据（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.UserProperties">
            <summary>
            用户属性（MQTT 5.0，JSON格式）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.ContentType">
            <summary>
            内容类型（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Message.PayloadFormatIndicator">
            <summary>
            负载格式指示符（MQTT 5.0）
            </summary>
        </member>
        <member name="T:MqttBroker.Data.Entities.RetainedMessage">
            <summary>
            MQTT保留消息实体
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.Topic">
            <summary>
            消息主题（主键）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.QoS">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.Timestamp">
            <summary>
            消息时间戳
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.ExpiryInterval">
            <summary>
            消息过期时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.PublisherClientId">
            <summary>
            发布者客户端ID
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.Properties">
            <summary>
            消息属性（MQTT 5.0，JSON格式）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.ResponseTopic">
            <summary>
            响应主题（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.CorrelationData">
            <summary>
            相关数据（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.UserProperties">
            <summary>
            用户属性（MQTT 5.0，JSON格式）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.ContentType">
            <summary>
            内容类型（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.PayloadFormatIndicator">
            <summary>
            负载格式指示符（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.UpdatedAt">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.AccessCount">
            <summary>
            访问次数
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.RetainedMessage.LastAccessedAt">
            <summary>
            最后访问时间
            </summary>
        </member>
        <member name="T:MqttBroker.Data.Entities.Session">
            <summary>
            MQTT会话实体
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.SessionId">
            <summary>
            会话ID（主键）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.ClientId">
            <summary>
            客户端ID（外键）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.IsPersistent">
            <summary>
            是否持久会话
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.LastActivity">
            <summary>
            最后活跃时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.ExpiresAt">
            <summary>
            会话过期时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.Status">
            <summary>
            会话状态
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.PendingMessagesCount">
            <summary>
            待发送消息数量
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.SubscriptionsCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.SessionData">
            <summary>
            会话数据（JSON格式存储额外信息）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.MaxQoS">
            <summary>
            最大QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.ReceiveRetainedMessages">
            <summary>
            是否接收保留消息
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Session.Properties">
            <summary>
            会话属性（MQTT 5.0）
            </summary>
        </member>
        <member name="T:MqttBroker.Data.Entities.Subscription">
            <summary>
            MQTT订阅实体
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.Id">
            <summary>
            订阅ID（主键）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.ClientId">
            <summary>
            客户端ID（外键）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.TopicFilter">
            <summary>
            主题过滤器
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.QoS">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.UpdatedAt">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.IsActive">
            <summary>
            是否活跃
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.SubscriptionOptions">
            <summary>
            订阅选项（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.SubscriptionIdentifier">
            <summary>
            订阅标识符（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.MatchedMessagesCount">
            <summary>
            匹配的消息数量
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.LastMatchedAt">
            <summary>
            最后匹配时间
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.Properties">
            <summary>
            订阅属性（MQTT 5.0，JSON格式）
            </summary>
        </member>
        <member name="P:MqttBroker.Data.Entities.Subscription.Notes">
            <summary>
            备注信息
            </summary>
        </member>
    </members>
</doc>
