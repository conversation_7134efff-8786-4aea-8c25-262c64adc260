using Xunit;
using FluentAssertions;
using MqttBroker.Core.Utilities;

namespace MqttBroker.Core.Tests.Utilities;

/// <summary>
/// TopicHelper 测试类
/// </summary>
public class TopicHelperTests
{
    [Theory]
    [InlineData("home/temperature", true)]
    [InlineData("sensors/room1/humidity", true)]
    [InlineData("device/123/status", true)]
    [InlineData("", false)]
    [InlineData(null, false)]
    [InlineData("home/+/temperature", false)] // 包含单级通配符
    [InlineData("home/#", false)] // 包含多级通配符
    [InlineData("$SYS/broker/version", false)] // 系统主题
    public void IsValidTopicName_Should_ValidateCorrectly(string topic, bool expected)
    {
        // Act
        var result = TopicHelper.IsValidTopicName(topic);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("home/temperature", true)]
    [InlineData("home/+/temperature", true)]
    [InlineData("home/#", true)]
    [InlineData("sensors/+/+/status", true)]
    [InlineData("", false)]
    [InlineData(null, false)]
    [InlineData("home/+temperature", false)] // 单级通配符不能与其他字符混合
    [InlineData("home/temp#", false)] // 多级通配符不能与其他字符混合
    [InlineData("home/#/temperature", false)] // 多级通配符不能在中间
    public void IsValidTopicFilter_Should_ValidateCorrectly(string topicFilter, bool expected)
    {
        // Act
        var result = TopicHelper.IsValidTopicFilter(topicFilter);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("home/temperature", "home/temperature", true)]
    [InlineData("home/temperature", "home/+", true)]
    [InlineData("home/living-room/temperature", "home/+/temperature", true)]
    [InlineData("home/living-room/temperature", "home/#", true)]
    [InlineData("home/living-room/temperature/sensor1", "home/#", true)]
    [InlineData("home/temperature", "office/temperature", false)]
    [InlineData("home/temperature", "home/+/temperature", false)]
    [InlineData("$SYS/broker/version", "home/+", false)] // 系统主题不匹配普通过滤器
    [InlineData("home/temperature", "$SYS/+", false)] // 普通主题不匹配系统过滤器
    public void IsTopicMatch_Should_MatchCorrectly(string topic, string topicFilter, bool expected)
    {
        // Act
        var result = TopicHelper.IsTopicMatch(topic, topicFilter);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("home", 1)]
    [InlineData("home/temperature", 2)]
    [InlineData("sensors/room1/temperature/value", 4)]
    [InlineData("", 0)]
    [InlineData("/", 2)] // 空级别也算一级
    public void GetTopicLevels_Should_ReturnCorrectCount(string topic, int expected)
    {
        // Act
        var result = TopicHelper.GetTopicLevels(topic);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("home/temperature", "home")]
    [InlineData("sensors/room1/temperature", "sensors/room1")]
    [InlineData("home", null)]
    [InlineData("", null)]
    public void GetParentTopic_Should_ReturnCorrectParent(string topic, string? expected)
    {
        // Act
        var result = TopicHelper.GetParentTopic(topic);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("home/temperature", "temperature")]
    [InlineData("sensors/room1/humidity", "humidity")]
    [InlineData("home", "home")]
    [InlineData("", "")]
    public void GetTopicLastLevel_Should_ReturnCorrectLevel(string topic, string expected)
    {
        // Act
        var result = TopicHelper.GetTopicLastLevel(topic);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("/home/<USER>/", "home/temperature")]
    [InlineData("home//temperature", "home/temperature")]
    [InlineData("//home///temperature//", "home/temperature")]
    [InlineData("home", "home")]
    public void NormalizeTopic_Should_CleanupTopic(string topic, string expected)
    {
        // Act
        var result = TopicHelper.NormalizeTopic(topic);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("$SYS/broker/version", true)]
    [InlineData("$share/group1/topic", true)]
    [InlineData("home/temperature", false)]
    [InlineData("", false)]
    public void IsSystemTopic_Should_IdentifyCorrectly(string topic, bool expected)
    {
        // Act
        var result = TopicHelper.IsSystemTopic(topic);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData("$share/group1/home/<USER>", true)]
    [InlineData("$share/group2/sensors/+", true)]
    [InlineData("home/temperature", false)]
    [InlineData("$SYS/broker/version", false)]
    public void IsSharedSubscription_Should_IdentifyCorrectly(string topicFilter, bool expected)
    {
        // Act
        var result = TopicHelper.IsSharedSubscription(topicFilter);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ParseSharedSubscription_Should_ParseCorrectly()
    {
        // Arrange
        var sharedFilter = "$share/group1/home/<USER>";

        // Act
        var (shareName, actualFilter) = TopicHelper.ParseSharedSubscription(sharedFilter);

        // Assert
        shareName.Should().Be("group1");
        actualFilter.Should().Be("home/temperature");
    }

    [Fact]
    public void ParseSharedSubscription_Should_ReturnOriginalForNonShared()
    {
        // Arrange
        var normalFilter = "home/temperature";

        // Act
        var (shareName, actualFilter) = TopicHelper.ParseSharedSubscription(normalFilter);

        // Assert
        shareName.Should().BeNull();
        actualFilter.Should().Be("home/temperature");
    }
}
