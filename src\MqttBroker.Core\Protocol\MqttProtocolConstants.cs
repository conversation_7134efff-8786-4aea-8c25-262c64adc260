namespace MqttBroker.Core.Protocol;

/// <summary>
/// MQTT协议常量
/// </summary>
public static class MqttProtocolConstants
{
    /// <summary>
    /// MQTT协议版本
    /// </summary>
    public static class ProtocolVersions
    {
        /// <summary>
        /// MQTT 3.1.1
        /// </summary>
        public const byte V311 = 4;

        /// <summary>
        /// MQTT 5.0
        /// </summary>
        public const byte V50 = 5;
    }

    /// <summary>
    /// MQTT消息类型
    /// </summary>
    public static class MessageTypes
    {
        /// <summary>
        /// 连接请求
        /// </summary>
        public const byte CONNECT = 1;

        /// <summary>
        /// 连接确认
        /// </summary>
        public const byte CONNACK = 2;

        /// <summary>
        /// 发布消息
        /// </summary>
        public const byte PUBLISH = 3;

        /// <summary>
        /// 发布确认
        /// </summary>
        public const byte PUBACK = 4;

        /// <summary>
        /// 发布收到
        /// </summary>
        public const byte PUBREC = 5;

        /// <summary>
        /// 发布释放
        /// </summary>
        public const byte PUBREL = 6;

        /// <summary>
        /// 发布完成
        /// </summary>
        public const byte PUBCOMP = 7;

        /// <summary>
        /// 订阅请求
        /// </summary>
        public const byte SUBSCRIBE = 8;

        /// <summary>
        /// 订阅确认
        /// </summary>
        public const byte SUBACK = 9;

        /// <summary>
        /// 取消订阅
        /// </summary>
        public const byte UNSUBSCRIBE = 10;

        /// <summary>
        /// 取消订阅确认
        /// </summary>
        public const byte UNSUBACK = 11;

        /// <summary>
        /// 心跳请求
        /// </summary>
        public const byte PINGREQ = 12;

        /// <summary>
        /// 心跳响应
        /// </summary>
        public const byte PINGRESP = 13;

        /// <summary>
        /// 断开连接
        /// </summary>
        public const byte DISCONNECT = 14;

        /// <summary>
        /// 认证（MQTT 5.0）
        /// </summary>
        public const byte AUTH = 15;
    }

    /// <summary>
    /// QoS等级
    /// </summary>
    public static class QoSLevels
    {
        /// <summary>
        /// 最多一次
        /// </summary>
        public const byte AtMostOnce = 0;

        /// <summary>
        /// 至少一次
        /// </summary>
        public const byte AtLeastOnce = 1;

        /// <summary>
        /// 恰好一次
        /// </summary>
        public const byte ExactlyOnce = 2;
    }

    /// <summary>
    /// 连接返回码
    /// </summary>
    public static class ConnectReturnCodes
    {
        /// <summary>
        /// 连接已接受
        /// </summary>
        public const byte Accepted = 0;

        /// <summary>
        /// 不可接受的协议版本
        /// </summary>
        public const byte UnacceptableProtocolVersion = 1;

        /// <summary>
        /// 标识符被拒绝
        /// </summary>
        public const byte IdentifierRejected = 2;

        /// <summary>
        /// 服务器不可用
        /// </summary>
        public const byte ServerUnavailable = 3;

        /// <summary>
        /// 错误的用户名或密码
        /// </summary>
        public const byte BadUsernameOrPassword = 4;

        /// <summary>
        /// 未授权
        /// </summary>
        public const byte NotAuthorized = 5;
    }

    /// <summary>
    /// 主题通配符
    /// </summary>
    public static class TopicWildcards
    {
        /// <summary>
        /// 单级通配符
        /// </summary>
        public const char SingleLevel = '+';

        /// <summary>
        /// 多级通配符
        /// </summary>
        public const char MultiLevel = '#';

        /// <summary>
        /// 主题分隔符
        /// </summary>
        public const char Separator = '/';
    }

    /// <summary>
    /// 默认值
    /// </summary>
    public static class Defaults
    {
        /// <summary>
        /// 默认端口
        /// </summary>
        public const int Port = 1883;

        /// <summary>
        /// 默认TLS端口
        /// </summary>
        public const int TlsPort = 8883;

        /// <summary>
        /// 默认WebSocket端口
        /// </summary>
        public const int WebSocketPort = 8080;

        /// <summary>
        /// 默认心跳间隔（秒）
        /// </summary>
        public const int KeepAlive = 60;

        /// <summary>
        /// 最大客户端ID长度
        /// </summary>
        public const int MaxClientIdLength = 128;

        /// <summary>
        /// 最大主题长度
        /// </summary>
        public const int MaxTopicLength = 512;

        /// <summary>
        /// 最大负载大小（字节）
        /// </summary>
        public const int MaxPayloadSize = 268435455; // 256MB - 1
    }

    /// <summary>
    /// MQTT 5.0 属性标识符
    /// </summary>
    public static class PropertyIds
    {
        public const byte PayloadFormatIndicator = 0x01;
        public const byte MessageExpiryInterval = 0x02;
        public const byte ContentType = 0x03;
        public const byte ResponseTopic = 0x08;
        public const byte CorrelationData = 0x09;
        public const byte SubscriptionIdentifier = 0x0B;
        public const byte SessionExpiryInterval = 0x11;
        public const byte AssignedClientIdentifier = 0x12;
        public const byte ServerKeepAlive = 0x13;
        public const byte AuthenticationMethod = 0x15;
        public const byte AuthenticationData = 0x16;
        public const byte RequestProblemInformation = 0x17;
        public const byte WillDelayInterval = 0x18;
        public const byte RequestResponseInformation = 0x19;
        public const byte ResponseInformation = 0x1A;
        public const byte ServerReference = 0x1C;
        public const byte ReasonString = 0x1F;
        public const byte ReceiveMaximum = 0x21;
        public const byte TopicAliasMaximum = 0x22;
        public const byte TopicAlias = 0x23;
        public const byte MaximumQoS = 0x24;
        public const byte RetainAvailable = 0x25;
        public const byte UserProperty = 0x26;
        public const byte MaximumPacketSize = 0x27;
        public const byte WildcardSubscriptionAvailable = 0x28;
        public const byte SubscriptionIdentifierAvailable = 0x29;
        public const byte SharedSubscriptionAvailable = 0x2A;
    }
}
