Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqttBroker.Core", "src\MqttBroker.Core\MqttBroker.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqttBroker.Web", "src\MqttBroker.Web\MqttBroker.Web.csproj", "{84FFD839-EED6-4153-8CAA-ECD4E820E601}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqttBroker.Data", "src\MqttBroker.Data\MqttBroker.Data.csproj", "{0D326033-67A4-4433-9EC9-7CE2B6C7611B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqttBroker.Console", "src\MqttBroker.Console\MqttBroker.Console.csproj", "{BC957612-82C7-4A55-AD31-28BA78C670A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqttBroker.Core.Tests", "tests\MqttBroker.Core.Tests\MqttBroker.Core.Tests.csproj", "{BA968ACB-09E3-41AC-B57C-1DC95D234610}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqttBroker.Web.Tests", "tests\MqttBroker.Web.Tests\MqttBroker.Web.Tests.csproj", "{918C5A13-B055-480F-9209-7F3A267559D4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqttBroker.Integration.Tests", "tests\MqttBroker.Integration.Tests\MqttBroker.Integration.Tests.csproj", "{C710B2DA-7FE0-40F6-BC62-63D6A4B38AF7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{04D3CD55-2048-47D0-AE54-B26602F178B3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{8C1571C1-9632-4D5B-8CB5-602C3226FF60}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{84FFD839-EED6-4153-8CAA-ECD4E820E601}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84FFD839-EED6-4153-8CAA-ECD4E820E601}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84FFD839-EED6-4153-8CAA-ECD4E820E601}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84FFD839-EED6-4153-8CAA-ECD4E820E601}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D326033-67A4-4433-9EC9-7CE2B6C7611B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D326033-67A4-4433-9EC9-7CE2B6C7611B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D326033-67A4-4433-9EC9-7CE2B6C7611B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D326033-67A4-4433-9EC9-7CE2B6C7611B}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC957612-82C7-4A55-AD31-28BA78C670A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC957612-82C7-4A55-AD31-28BA78C670A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC957612-82C7-4A55-AD31-28BA78C670A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC957612-82C7-4A55-AD31-28BA78C670A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{BA968ACB-09E3-41AC-B57C-1DC95D234610}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BA968ACB-09E3-41AC-B57C-1DC95D234610}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BA968ACB-09E3-41AC-B57C-1DC95D234610}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BA968ACB-09E3-41AC-B57C-1DC95D234610}.Release|Any CPU.Build.0 = Release|Any CPU
		{918C5A13-B055-480F-9209-7F3A267559D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{918C5A13-B055-480F-9209-7F3A267559D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{918C5A13-B055-480F-9209-7F3A267559D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{918C5A13-B055-480F-9209-7F3A267559D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{C710B2DA-7FE0-40F6-BC62-63D6A4B38AF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C710B2DA-7FE0-40F6-BC62-63D6A4B38AF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C710B2DA-7FE0-40F6-BC62-63D6A4B38AF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C710B2DA-7FE0-40F6-BC62-63D6A4B38AF7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
