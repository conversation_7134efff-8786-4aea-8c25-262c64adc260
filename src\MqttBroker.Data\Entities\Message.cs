using System.ComponentModel.DataAnnotations;

namespace MqttBroker.Data.Entities;

/// <summary>
/// MQTT消息实体
/// </summary>
public class Message
{
    /// <summary>
    /// 消息ID（主键）
    /// </summary>
    [Key]
    public long Id { get; set; }

    /// <summary>
    /// 消息主题
    /// </summary>
    [Required]
    [StringLength(512)]
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 消息负载
    /// </summary>
    [Required]
    public byte[] Payload { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// QoS等级
    /// </summary>
    [Range(0, 2)]
    public byte QoS { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 消息时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息过期时间
    /// </summary>
    public DateTime? ExpiryInterval { get; set; }

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    [StringLength(128)]
    public string? PublisherClientId { get; set; }

    /// <summary>
    /// 消息标识符（用于QoS 1和2）
    /// </summary>
    public int? MessageId { get; set; }

    /// <summary>
    /// 重复标志
    /// </summary>
    public bool Duplicate { get; set; }

    /// <summary>
    /// 消息状态
    /// </summary>
    [StringLength(20)]
    public string Status { get; set; } = "Published";

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 最后重试时间
    /// </summary>
    public DateTime? LastRetryAt { get; set; }

    /// <summary>
    /// 消息属性（MQTT 5.0，JSON格式）
    /// </summary>
    public string? Properties { get; set; }

    /// <summary>
    /// 响应主题（MQTT 5.0）
    /// </summary>
    [StringLength(512)]
    public string? ResponseTopic { get; set; }

    /// <summary>
    /// 相关数据（MQTT 5.0）
    /// </summary>
    public byte[]? CorrelationData { get; set; }

    /// <summary>
    /// 用户属性（MQTT 5.0，JSON格式）
    /// </summary>
    public string? UserProperties { get; set; }

    /// <summary>
    /// 内容类型（MQTT 5.0）
    /// </summary>
    [StringLength(256)]
    public string? ContentType { get; set; }

    /// <summary>
    /// 负载格式指示符（MQTT 5.0）
    /// </summary>
    public byte? PayloadFormatIndicator { get; set; }
}
