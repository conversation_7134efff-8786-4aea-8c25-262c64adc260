D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\appsettings.json
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Console.exe
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Console.deps.json
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Console.runtimeconfig.json
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Console.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Console.pdb
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Azure.Core.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Azure.Identity.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Data.Sqlite.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Sqlite.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Json.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Physical.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Hosting.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Logging.Configuration.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Logging.Console.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Logging.Debug.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Logging.EventLog.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Logging.EventSource.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Identity.Client.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MQTTnet.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Serilog.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\Serilog.Sinks.Console.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\SQLitePCLRaw.batteries_v2.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\SQLitePCLRaw.core.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\SQLitePCLRaw.provider.e_sqlite3.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.ClientModel.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Diagnostics.EventLog.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Drawing.Common.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Memory.Data.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Runtime.Caching.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Security.Permissions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Windows.Extensions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\browser-wasm\nativeassets\net8.0\e_sqlite3.a
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-arm\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-arm64\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-armel\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-mips64\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-s390x\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-x64\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-x86\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\osx-x64\native\libe_sqlite3.dylib
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-arm\native\e_sqlite3.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-arm64\native\e_sqlite3.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-x64\native\e_sqlite3.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win-x86\native\e_sqlite3.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.Messages.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Core.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Data.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Core.pdb
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Core.xml
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Data.pdb
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\MqttBroker.Data.xml
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.csproj.AssemblyReference.cache
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.GeneratedMSBuildEditorConfig.editorconfig
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.AssemblyInfoInputs.cache
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.AssemblyInfo.cs
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.csproj.CoreCompileInputs.cache
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBrok.F334E1C3.Up2Date
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.dll
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\refint\MqttBroker.Console.dll
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.pdb
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\MqttBroker.Console.genruntimeconfig.cache
D:\01 Broker\src\MqttBroker.Console\obj\Debug\net8.0\ref\MqttBroker.Console.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Formats.Asn1.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.IO.Pipelines.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Text.Encodings.Web.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Text.Json.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\System.Threading.Channels.dll
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\linux-musl-s390x\native\libe_sqlite3.so
D:\01 Broker\src\MqttBroker.Console\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
