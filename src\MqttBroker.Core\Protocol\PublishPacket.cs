using MqttBroker.Core.Models;

namespace MqttBroker.Core.Protocol;

/// <summary>
/// PUBLISH数据包
/// </summary>
public class PublishPacket : MqttPacket
{
    public override byte MessageType => MqttProtocolConstants.MessageTypes.PUBLISH;

    /// <summary>
    /// 主题名称
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 消息负载
    /// </summary>
    public byte[] Payload { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// QoS等级
    /// </summary>
    public MqttQoSLevel QoS { get; set; } = MqttQoSLevel.AtMostOnce;

    /// <summary>
    /// 保留标志
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 重复标志
    /// </summary>
    public bool Duplicate { get; set; }

    /// <summary>
    /// MQTT 5.0 属性
    /// </summary>
    public Dictionary<byte, object>? Properties { get; set; }

    public override byte[] Serialize()
    {
        using var stream = new MemoryStream();
        using var writer = new BinaryWriter(stream);

        // 固定头部
        var flags = (byte)0x00;
        if (Duplicate) flags |= 0x08;
        flags |= (byte)((byte)QoS << 1);
        if (Retain) flags |= 0x01;

        writer.Write((byte)((MessageType << 4) | flags));

        // 可变头部和负载
        using var payloadStream = new MemoryStream();
        using var payloadWriter = new BinaryWriter(payloadStream);

        // 主题名称
        WriteString(payloadWriter, Topic);

        // 数据包标识符（QoS > 0时需要）
        if (QoS > MqttQoSLevel.AtMostOnce)
        {
            var packetId = PacketIdentifier ?? 0;
            payloadWriter.Write((byte)(packetId >> 8));
            payloadWriter.Write((byte)(packetId & 0xFF));
        }

        // MQTT 5.0 属性
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            WriteProperties(payloadWriter, Properties);
        }

        // 消息负载
        payloadWriter.Write(Payload);

        var payload = payloadStream.ToArray();

        // 剩余长度
        WriteRemainingLength(writer, payload.Length);

        // 写入负载
        writer.Write(payload);

        return stream.ToArray();
    }

    public override bool Deserialize(byte[] buffer, int offset, int length)
    {
        try
        {
            var reader = new BinaryReader(new MemoryStream(buffer, offset, length));

            // 解析固定头部标志
            var firstByte = buffer[offset];
            Duplicate = (firstByte & 0x08) != 0;
            QoS = (MqttQoSLevel)((firstByte >> 1) & 0x03);
            Retain = (firstByte & 0x01) != 0;

            // 跳过固定头部
            reader.ReadByte(); // 消息类型和标志
            var remainingLength = ReadRemainingLength(reader);

            // 主题名称
            Topic = ReadString(reader);

            // 数据包标识符（QoS > 0时存在）
            if (QoS > MqttQoSLevel.AtMostOnce)
            {
                PacketIdentifier = (reader.ReadByte() << 8) | reader.ReadByte();
            }

            // MQTT 5.0 属性
            if (ProtocolVersion == MqttProtocolVersion.V50)
            {
                Properties = ReadProperties(reader);
            }

            // 计算负载长度
            var headerSize = 2 + Topic.Length; // 主题长度字段 + 主题
            if (QoS > MqttQoSLevel.AtMostOnce)
                headerSize += 2; // 数据包标识符

            if (ProtocolVersion == MqttProtocolVersion.V50)
            {
                headerSize += GetPropertiesSize(Properties);
            }

            var payloadLength = remainingLength - headerSize;
            if (payloadLength > 0)
            {
                Payload = reader.ReadBytes(payloadLength);
            }
            else
            {
                Payload = Array.Empty<byte>();
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    public override bool IsValid()
    {
        // 验证主题名称
        if (string.IsNullOrEmpty(Topic))
            return false;

        if (Topic.Length > MqttProtocolConstants.Defaults.MaxTopicLength)
            return false;

        // 主题名称不能包含通配符
        if (Topic.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) ||
            Topic.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
            return false;

        // 验证QoS等级
        if (!Enum.IsDefined(typeof(MqttQoSLevel), QoS))
            return false;

        // QoS > 0时必须有数据包标识符
        if (QoS > MqttQoSLevel.AtMostOnce && PacketIdentifier == null)
            return false;

        // 验证负载大小
        if (Payload.Length > MqttProtocolConstants.Defaults.MaxPayloadSize)
            return false;

        return true;
    }

    public override int GetPacketSize()
    {
        var size = 1; // 固定头部消息类型和标志

        // 主题名称长度
        size += 2 + Topic.Length;

        // 数据包标识符
        if (QoS > MqttQoSLevel.AtMostOnce)
            size += 2;

        // MQTT 5.0 属性长度
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            size += GetPropertiesSize(Properties);
        }

        // 负载长度
        size += Payload.Length;

        // 剩余长度字段大小
        size += GetRemainingLengthSize(size - 1);

        return size;
    }

    #region 辅助方法

    private static void WriteString(BinaryWriter writer, string value)
    {
        var bytes = System.Text.Encoding.UTF8.GetBytes(value);
        writer.Write((byte)(bytes.Length >> 8));
        writer.Write((byte)(bytes.Length & 0xFF));
        writer.Write(bytes);
    }

    private static string ReadString(BinaryReader reader)
    {
        var length = (reader.ReadByte() << 8) | reader.ReadByte();
        var bytes = reader.ReadBytes(length);
        return System.Text.Encoding.UTF8.GetString(bytes);
    }

    private static void WriteRemainingLength(BinaryWriter writer, int length)
    {
        do
        {
            var encodedByte = (byte)(length % 128);
            length /= 128;
            if (length > 0)
                encodedByte |= 128;
            writer.Write(encodedByte);
        } while (length > 0);
    }

    private static int ReadRemainingLength(BinaryReader reader)
    {
        var multiplier = 1;
        var value = 0;
        byte encodedByte;

        do
        {
            encodedByte = reader.ReadByte();
            value += (encodedByte & 127) * multiplier;
            multiplier *= 128;
        } while ((encodedByte & 128) != 0);

        return value;
    }

    private static int GetRemainingLengthSize(int length)
    {
        if (length < 128) return 1;
        if (length < 16384) return 2;
        if (length < 2097152) return 3;
        return 4;
    }

    private static void WriteProperties(BinaryWriter writer, Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
        {
            writer.Write((byte)0);
            return;
        }
        // TODO: 实现MQTT 5.0属性序列化
        writer.Write((byte)0);
    }

    private static Dictionary<byte, object>? ReadProperties(BinaryReader reader)
    {
        var length = reader.ReadByte();
        if (length == 0)
            return null;
        // TODO: 实现MQTT 5.0属性反序列化
        reader.ReadBytes(length);
        return new Dictionary<byte, object>();
    }

    private static int GetPropertiesSize(Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
            return 1;
        // TODO: 计算实际属性大小
        return 1;
    }

    #endregion
}

/// <summary>
/// PUBACK数据包
/// </summary>
public class PubackPacket : MqttPacket
{
    public override byte MessageType => MqttProtocolConstants.MessageTypes.PUBACK;

    /// <summary>
    /// 原因码（MQTT 5.0）
    /// </summary>
    public ReasonCode ReasonCode { get; set; } = ReasonCode.Success;

    /// <summary>
    /// MQTT 5.0 属性
    /// </summary>
    public Dictionary<byte, object>? Properties { get; set; }

    public override byte[] Serialize()
    {
        using var stream = new MemoryStream();
        using var writer = new BinaryWriter(stream);

        // 固定头部
        writer.Write((byte)((MessageType << 4) | 0x00));

        // 计算剩余长度
        var remainingLength = 2; // 数据包标识符
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            remainingLength += 1; // 原因码
            remainingLength += GetPropertiesSize(Properties);
        }

        WriteRemainingLength(writer, remainingLength);

        // 数据包标识符
        var packetId = PacketIdentifier ?? 0;
        writer.Write((byte)(packetId >> 8));
        writer.Write((byte)(packetId & 0xFF));

        // MQTT 5.0 原因码和属性
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            writer.Write((byte)ReasonCode);
            WriteProperties(writer, Properties);
        }

        return stream.ToArray();
    }

    public override bool Deserialize(byte[] buffer, int offset, int length)
    {
        try
        {
            var reader = new BinaryReader(new MemoryStream(buffer, offset, length));

            // 跳过固定头部
            reader.ReadByte();
            var remainingLength = ReadRemainingLength(reader);

            // 数据包标识符
            PacketIdentifier = (reader.ReadByte() << 8) | reader.ReadByte();

            // MQTT 5.0 原因码和属性
            if (ProtocolVersion == MqttProtocolVersion.V50 && remainingLength > 2)
            {
                ReasonCode = (ReasonCode)reader.ReadByte();
                if (remainingLength > 3)
                {
                    Properties = ReadProperties(reader);
                }
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    public override bool IsValid()
    {
        return PacketIdentifier.HasValue && PacketIdentifier > 0;
    }

    public override int GetPacketSize()
    {
        var size = 1; // 固定头部
        size += 2; // 数据包标识符

        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            size += 1; // 原因码
            size += GetPropertiesSize(Properties);
        }

        size += GetRemainingLengthSize(size - 1);
        return size;
    }

    #region 辅助方法 (复用其他类中的方法)

    private static void WriteRemainingLength(BinaryWriter writer, int length)
    {
        do
        {
            var encodedByte = (byte)(length % 128);
            length /= 128;
            if (length > 0)
                encodedByte |= 128;
            writer.Write(encodedByte);
        } while (length > 0);
    }

    private static int ReadRemainingLength(BinaryReader reader)
    {
        var multiplier = 1;
        var value = 0;
        byte encodedByte;

        do
        {
            encodedByte = reader.ReadByte();
            value += (encodedByte & 127) * multiplier;
            multiplier *= 128;
        } while ((encodedByte & 128) != 0);

        return value;
    }

    private static int GetRemainingLengthSize(int length)
    {
        if (length < 128) return 1;
        if (length < 16384) return 2;
        if (length < 2097152) return 3;
        return 4;
    }

    private static void WriteProperties(BinaryWriter writer, Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
        {
            writer.Write((byte)0);
            return;
        }
        writer.Write((byte)0);
    }

    private static Dictionary<byte, object>? ReadProperties(BinaryReader reader)
    {
        var length = reader.ReadByte();
        if (length == 0)
            return null;
        reader.ReadBytes(length);
        return new Dictionary<byte, object>();
    }

    private static int GetPropertiesSize(Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
            return 1;
        return 1;
    }

    #endregion
}
