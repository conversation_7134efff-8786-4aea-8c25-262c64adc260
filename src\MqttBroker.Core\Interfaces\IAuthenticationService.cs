using MqttBroker.Core.Models;

namespace MqttBroker.Core.Interfaces;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// 验证客户端连接
    /// </summary>
    /// <param name="request">认证请求</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> AuthenticateAsync(AuthenticationRequest request);

    /// <summary>
    /// 验证客户端ID
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否有效</returns>
    Task<bool> ValidateClientIdAsync(string clientId);

    /// <summary>
    /// 验证用户名和密码
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <returns>是否有效</returns>
    Task<bool> ValidateCredentialsAsync(string? username, string? password);

    /// <summary>
    /// 生成客户端ID
    /// </summary>
    /// <returns>生成的客户端ID</returns>
    Task<string> GenerateClientIdAsync();

    /// <summary>
    /// 检查客户端是否被禁用
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否被禁用</returns>
    Task<bool> IsClientDisabledAsync(string clientId);
}

/// <summary>
/// 授权服务接口
/// </summary>
public interface IAuthorizationService
{
    /// <summary>
    /// 检查发布权限
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topic">主题</param>
    /// <returns>是否有权限</returns>
    Task<bool> CanPublishAsync(string clientId, string topic);

    /// <summary>
    /// 检查订阅权限
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否有权限</returns>
    Task<bool> CanSubscribeAsync(string clientId, string topicFilter);

    /// <summary>
    /// 检查连接权限
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="ipAddress">IP地址</param>
    /// <returns>是否有权限</returns>
    Task<bool> CanConnectAsync(string clientId, string? ipAddress);

    /// <summary>
    /// 获取客户端的最大QoS等级
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>最大QoS等级</returns>
    Task<MqttQoSLevel> GetMaxQoSAsync(string clientId);

    /// <summary>
    /// 获取客户端的权限列表
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>权限列表</returns>
    Task<IEnumerable<Permission>> GetPermissionsAsync(string clientId);
}

/// <summary>
/// 认证请求
/// </summary>
public class AuthenticationRequest
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// 协议版本
    /// </summary>
    public MqttProtocolVersion ProtocolVersion { get; set; }

    /// <summary>
    /// 是否清理会话
    /// </summary>
    public bool CleanSession { get; set; }

    /// <summary>
    /// 心跳间隔
    /// </summary>
    public TimeSpan KeepAlive { get; set; }

    /// <summary>
    /// 遗嘱消息
    /// </summary>
    public WillMessage? WillMessage { get; set; }

    /// <summary>
    /// 客户端证书（用于TLS认证）
    /// </summary>
    public byte[]? ClientCertificate { get; set; }
}

/// <summary>
/// 认证结果
/// </summary>
public class AuthenticationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 返回码
    /// </summary>
    public ConnectReturnCode ReturnCode { get; set; }

    /// <summary>
    /// 原因码（MQTT 5.0）
    /// </summary>
    public ReasonCode? ReasonCode { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 分配的客户端ID（如果原始ID为空）
    /// </summary>
    public string? AssignedClientId { get; set; }

    /// <summary>
    /// 会话是否存在
    /// </summary>
    public bool SessionPresent { get; set; }

    /// <summary>
    /// 服务器保持活跃时间
    /// </summary>
    public TimeSpan? ServerKeepAlive { get; set; }

    /// <summary>
    /// 最大QoS等级
    /// </summary>
    public MqttQoSLevel MaxQoS { get; set; } = MqttQoSLevel.ExactlyOnce;

    /// <summary>
    /// 是否支持保留消息
    /// </summary>
    public bool RetainAvailable { get; set; } = true;

    /// <summary>
    /// 是否支持通配符订阅
    /// </summary>
    public bool WildcardSubscriptionAvailable { get; set; } = true;

    /// <summary>
    /// 是否支持订阅标识符
    /// </summary>
    public bool SubscriptionIdentifierAvailable { get; set; } = true;

    /// <summary>
    /// 是否支持共享订阅
    /// </summary>
    public bool SharedSubscriptionAvailable { get; set; } = false;
}

/// <summary>
/// 遗嘱消息
/// </summary>
public class WillMessage
{
    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 负载
    /// </summary>
    public byte[] Payload { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// QoS等级
    /// </summary>
    public MqttQoSLevel QoS { get; set; }

    /// <summary>
    /// 是否保留
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 延迟间隔（MQTT 5.0）
    /// </summary>
    public TimeSpan? DelayInterval { get; set; }
}

/// <summary>
/// 权限
/// </summary>
public class Permission
{
    /// <summary>
    /// 权限类型
    /// </summary>
    public PermissionType Type { get; set; }

    /// <summary>
    /// 主题模式
    /// </summary>
    public string TopicPattern { get; set; } = string.Empty;

    /// <summary>
    /// 是否允许
    /// </summary>
    public bool Allow { get; set; }

    /// <summary>
    /// 最大QoS等级
    /// </summary>
    public MqttQoSLevel? MaxQoS { get; set; }
}

/// <summary>
/// 权限类型
/// </summary>
public enum PermissionType
{
    /// <summary>
    /// 发布权限
    /// </summary>
    Publish,

    /// <summary>
    /// 订阅权限
    /// </summary>
    Subscribe,

    /// <summary>
    /// 连接权限
    /// </summary>
    Connect
}
