using Xunit;
using FluentAssertions;
using MqttBroker.Core.Services;

namespace MqttBroker.Core.Tests.Services;

/// <summary>
/// MQTT Broker 服务测试
/// </summary>
public class MqttBrokerServiceTests
{
    /// <summary>
    /// 测试BrokerStatistics类的基本功能
    /// </summary>
    [Fact]
    public void BrokerStatistics_Should_CalculateUptime_Correctly()
    {
        // Arrange
        var startTime = DateTime.UtcNow.AddMinutes(-5);
        var statistics = new BrokerStatistics
        {
            StartTime = startTime,
            ConnectedClients = 10,
            TotalSubscriptions = 25,
            RetainedMessages = 5,
            PublishedMessages = 100,
            ReceivedMessages = 95
        };

        // Act
        var uptime = statistics.Uptime;

        // Assert
        uptime.Should().BeGreaterThan(TimeSpan.FromMinutes(4));
        uptime.Should().BeLessThan(TimeSpan.FromMinutes(6));
        statistics.ConnectedClients.Should().Be(10);
        statistics.TotalSubscriptions.Should().Be(25);
        statistics.RetainedMessages.Should().Be(5);
        statistics.PublishedMessages.Should().Be(100);
        statistics.ReceivedMessages.Should().Be(95);
    }

    /// <summary>
    /// 测试BrokerStatistics的默认值
    /// </summary>
    [Fact]
    public void BrokerStatistics_Should_HaveCorrectDefaultValues()
    {
        // Arrange & Act
        var statistics = new BrokerStatistics();

        // Assert
        statistics.ConnectedClients.Should().Be(0);
        statistics.TotalSubscriptions.Should().Be(0);
        statistics.RetainedMessages.Should().Be(0);
        statistics.PublishedMessages.Should().Be(0);
        statistics.ReceivedMessages.Should().Be(0);
        statistics.StartTime.Should().Be(default(DateTime));
    }

    /// <summary>
    /// 测试BrokerStatistics的属性设置
    /// </summary>
    [Theory]
    [InlineData(0, 0, 0, 0, 0)]
    [InlineData(1, 5, 2, 10, 8)]
    [InlineData(100, 500, 50, 1000, 950)]
    public void BrokerStatistics_Should_SetPropertiesCorrectly(
        int connectedClients, 
        int totalSubscriptions, 
        int retainedMessages, 
        long publishedMessages, 
        long receivedMessages)
    {
        // Arrange
        var startTime = DateTime.UtcNow;
        var statistics = new BrokerStatistics();

        // Act
        statistics.ConnectedClients = connectedClients;
        statistics.TotalSubscriptions = totalSubscriptions;
        statistics.RetainedMessages = retainedMessages;
        statistics.PublishedMessages = publishedMessages;
        statistics.ReceivedMessages = receivedMessages;
        statistics.StartTime = startTime;

        // Assert
        statistics.ConnectedClients.Should().Be(connectedClients);
        statistics.TotalSubscriptions.Should().Be(totalSubscriptions);
        statistics.RetainedMessages.Should().Be(retainedMessages);
        statistics.PublishedMessages.Should().Be(publishedMessages);
        statistics.ReceivedMessages.Should().Be(receivedMessages);
        statistics.StartTime.Should().Be(startTime);
    }
}
