using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using MQTTnet;

namespace MqttBroker.Console;

/// <summary>
/// MQTT Broker 控制台演示程序
/// </summary>
class Program
{
    private static IConfiguration? _configuration;
    private static ILogger<Program>? _logger;

    static async Task Main(string[] args)
    {
        // 配置Serilog
        var configuration = GetConfiguration();
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();

        try
        {
            Log.Information("启动 MQTT Broker 控制台演示程序");

            // 创建主机
            var host = CreateHostBuilder(args).Build();
            
            // 获取服务
            _logger = host.Services.GetRequiredService<ILogger<Program>>();
            _configuration = host.Services.GetRequiredService<IConfiguration>();

            // 显示欢迎信息
            ShowWelcomeMessage();

            // 启动演示
            await RunDemoAsync();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序运行失败");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// 创建主机构建器
    /// </summary>
    static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((context, services) =>
            {
                // 添加配置
                services.Configure<TestClientOptions>(
                    context.Configuration.GetSection("TestClient"));
                
                // TODO: 添加其他服务
            });

    /// <summary>
    /// 显示欢迎信息
    /// </summary>
    static void ShowWelcomeMessage()
    {
        System.Console.Clear();
        System.Console.WriteLine("=".PadRight(60, '='));
        System.Console.WriteLine("    MQTT Broker 控制台演示程序");
        System.Console.WriteLine("=".PadRight(60, '='));
        System.Console.WriteLine();
        
        var serverHost = _configuration?["TestClient:ServerHost"] ?? "localhost";
        var serverPort = _configuration?["TestClient:ServerPort"] ?? "1883";
        
        System.Console.WriteLine($"目标服务器: {serverHost}:{serverPort}");
        System.Console.WriteLine();
        System.Console.WriteLine("可用命令:");
        System.Console.WriteLine("  1 - 连接测试");
        System.Console.WriteLine("  2 - 发布消息测试");
        System.Console.WriteLine("  3 - 订阅消息测试");
        System.Console.WriteLine("  4 - 性能测试");
        System.Console.WriteLine("  5 - 自动化测试");
        System.Console.WriteLine("  q - 退出程序");
        System.Console.WriteLine();
    }

    /// <summary>
    /// 运行演示
    /// </summary>
    static async Task RunDemoAsync()
    {
        while (true)
        {
            System.Console.Write("请选择操作 (1-5, q): ");
            var input = System.Console.ReadLine()?.Trim().ToLower();

            switch (input)
            {
                case "1":
                    await TestConnectionAsync();
                    break;
                case "2":
                    await TestPublishAsync();
                    break;
                case "3":
                    await TestSubscribeAsync();
                    break;
                case "4":
                    await TestPerformanceAsync();
                    break;
                case "5":
                    await RunAutomatedTestsAsync();
                    break;
                case "q":
                case "quit":
                case "exit":
                    System.Console.WriteLine("程序退出。");
                    return;
                default:
                    System.Console.WriteLine("无效选择，请重试。");
                    break;
            }

            System.Console.WriteLine();
            System.Console.WriteLine("按任意键继续...");
            System.Console.ReadKey();
            ShowWelcomeMessage();
        }
    }

    /// <summary>
    /// 测试连接
    /// </summary>
    static async Task TestConnectionAsync()
    {
        System.Console.WriteLine("正在测试连接...");
        
        try
        {
            var factory = new MqttClientFactory();
            using var client = factory.CreateMqttClient();

            var options = new MqttClientOptionsBuilder()
                .WithTcpServer(_configuration?["TestClient:ServerHost"] ?? "localhost", 
                              int.Parse(_configuration?["TestClient:ServerPort"] ?? "1883"))
                .WithClientId(_configuration?["TestClient:ClientId"] ?? "TestClient")
                .WithCleanSession()
                .Build();

            var result = await client.ConnectAsync(options);
            
            if (result.ResultCode == MqttClientConnectResultCode.Success)
            {
                System.Console.WriteLine("✓ 连接成功！");
                await client.DisconnectAsync();
                System.Console.WriteLine("✓ 断开连接成功！");
            }
            else
            {
                System.Console.WriteLine($"✗ 连接失败: {result.ResultCode}");
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"✗ 连接异常: {ex.Message}");
            _logger?.LogError(ex, "连接测试失败");
        }
    }

    /// <summary>
    /// 测试发布消息
    /// </summary>
    static async Task TestPublishAsync()
    {
        System.Console.WriteLine("正在测试发布消息...");
        
        // TODO: 实现发布消息测试
        System.Console.WriteLine("发布消息测试功能待实现");
    }

    /// <summary>
    /// 测试订阅消息
    /// </summary>
    static async Task TestSubscribeAsync()
    {
        System.Console.WriteLine("正在测试订阅消息...");
        
        // TODO: 实现订阅消息测试
        System.Console.WriteLine("订阅消息测试功能待实现");
    }

    /// <summary>
    /// 性能测试
    /// </summary>
    static async Task TestPerformanceAsync()
    {
        System.Console.WriteLine("正在进行性能测试...");
        
        // TODO: 实现性能测试
        System.Console.WriteLine("性能测试功能待实现");
    }

    /// <summary>
    /// 运行自动化测试
    /// </summary>
    static async Task RunAutomatedTestsAsync()
    {
        System.Console.WriteLine("正在运行自动化测试...");
        
        // TODO: 实现自动化测试
        System.Console.WriteLine("自动化测试功能待实现");
    }

    /// <summary>
    /// 获取配置
    /// </summary>
    static IConfiguration GetConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddEnvironmentVariables()
            .Build();
    }
}

/// <summary>
/// 测试客户端配置选项
/// </summary>
public class TestClientOptions
{
    public string ServerHost { get; set; } = "localhost";
    public int ServerPort { get; set; } = 1883;
    public string ClientId { get; set; } = "TestClient";
    public string Username { get; set; } = "";
    public string Password { get; set; } = "";
    public string[] TestTopics { get; set; } = Array.Empty<string>();
}
