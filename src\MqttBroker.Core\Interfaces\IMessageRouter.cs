using MqttBroker.Core.Models;

namespace MqttBroker.Core.Interfaces;

/// <summary>
/// 消息路由器接口
/// </summary>
public interface IMessageRouter
{
    /// <summary>
    /// 路由消息到订阅者
    /// </summary>
    /// <param name="message">要路由的消息</param>
    /// <returns>路由任务</returns>
    Task RouteMessageAsync(IMqttMessage message);

    /// <summary>
    /// 添加订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="qos">QoS等级</param>
    /// <param name="options">订阅选项</param>
    /// <returns>是否添加成功</returns>
    Task<bool> AddSubscriptionAsync(string clientId, string topicFilter, MqttQoSLevel qos, SubscriptionOptions options = SubscriptionOptions.None);

    /// <summary>
    /// 移除订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否移除成功</returns>
    Task<bool> RemoveSubscriptionAsync(string clientId, string topicFilter);

    /// <summary>
    /// 移除客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>移除的订阅数量</returns>
    Task<int> RemoveAllSubscriptionsAsync(string clientId);

    /// <summary>
    /// 获取主题的订阅者
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>订阅者列表</returns>
    Task<IEnumerable<ISubscription>> GetSubscribersAsync(string topic);

    /// <summary>
    /// 获取客户端的订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>订阅列表</returns>
    Task<IEnumerable<ISubscription>> GetClientSubscriptionsAsync(string clientId);

    /// <summary>
    /// 检查主题是否匹配过滤器
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否匹配</returns>
    bool IsTopicMatch(string topic, string topicFilter);

    /// <summary>
    /// 验证主题过滤器
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否有效</returns>
    bool IsValidTopicFilter(string topicFilter);

    /// <summary>
    /// 验证主题名称
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <returns>是否有效</returns>
    bool IsValidTopicName(string topic);
}

/// <summary>
/// 订阅接口
/// </summary>
public interface ISubscription
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    int Id { get; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    string ClientId { get; }

    /// <summary>
    /// 主题过滤器
    /// </summary>
    string TopicFilter { get; }

    /// <summary>
    /// QoS等级
    /// </summary>
    MqttQoSLevel QoS { get; }

    /// <summary>
    /// 订阅选项
    /// </summary>
    SubscriptionOptions Options { get; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    DateTime CreatedAt { get; }

    /// <summary>
    /// 是否活跃
    /// </summary>
    bool IsActive { get; }

    /// <summary>
    /// 匹配的消息数量
    /// </summary>
    long MatchedMessagesCount { get; }

    /// <summary>
    /// 最后匹配时间
    /// </summary>
    DateTime? LastMatchedAt { get; }

    /// <summary>
    /// 订阅标识符（MQTT 5.0）
    /// </summary>
    int? SubscriptionIdentifier { get; }
}

/// <summary>
/// 保留消息管理器接口
/// </summary>
public interface IRetainedMessageManager
{
    /// <summary>
    /// 设置保留消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="message">消息</param>
    /// <returns>设置任务</returns>
    Task SetRetainedMessageAsync(string topic, IMqttMessage message);

    /// <summary>
    /// 获取保留消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>保留消息</returns>
    Task<IMqttMessage?> GetRetainedMessageAsync(string topic);

    /// <summary>
    /// 删除保留消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteRetainedMessageAsync(string topic);

    /// <summary>
    /// 获取匹配主题过滤器的保留消息
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>保留消息列表</returns>
    Task<IEnumerable<IMqttMessage>> GetRetainedMessagesAsync(string topicFilter);

    /// <summary>
    /// 清理过期的保留消息
    /// </summary>
    /// <returns>清理的消息数量</returns>
    Task<int> CleanupExpiredMessagesAsync();

    /// <summary>
    /// 获取保留消息数量
    /// </summary>
    /// <returns>消息数量</returns>
    Task<int> GetRetainedMessageCountAsync();

    /// <summary>
    /// 获取所有保留消息的主题
    /// </summary>
    /// <returns>主题列表</returns>
    Task<IEnumerable<string>> GetRetainedTopicsAsync();
}
