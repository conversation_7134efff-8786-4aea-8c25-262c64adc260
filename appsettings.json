{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=MqttBrokerDb;Trusted_Connection=true;MultipleActiveResultSets=true", "SqliteConnection": "Data Source=mqttbroker.db"}, "MqttBroker": {"ServerOptions": {"Port": 1883, "TlsPort": 8883, "WebSocketPort": 8080, "MaxPendingMessagesPerClient": 250, "DefaultCommunicationTimeout": "00:01:00", "EnablePersistentSessions": true, "MaxRetainedMessages": 5000}, "Authentication": {"AllowAnonymous": false, "RequireClientId": true, "ValidateClientId": true}, "Persistence": {"Provider": "EntityFramework", "RetainedMessagesCount": 1000, "QoSMessagesCount": 1000}, "Logging": {"LogClientConnections": true, "LogPublishedMessages": false, "LogSubscriptions": true}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/mqttbroker-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}