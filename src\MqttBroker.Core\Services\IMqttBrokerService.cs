namespace MqttBroker.Core.Services;

/// <summary>
/// MQTT Broker 核心服务接口
/// </summary>
public interface IMqttBrokerService
{
    /// <summary>
    /// 启动MQTT Broker服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止MQTT Broker服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>服务是否正在运行</returns>
    bool IsRunning { get; }

    /// <summary>
    /// 获取连接的客户端数量
    /// </summary>
    /// <returns>客户端数量</returns>
    int ConnectedClientsCount { get; }

    /// <summary>
    /// 获取服务统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<BrokerStatistics> GetStatisticsAsync();
}

/// <summary>
/// Broker统计信息
/// </summary>
public class BrokerStatistics
{
    /// <summary>
    /// 连接的客户端数量
    /// </summary>
    public int ConnectedClients { get; set; }

    /// <summary>
    /// 总订阅数量
    /// </summary>
    public int TotalSubscriptions { get; set; }

    /// <summary>
    /// 保留消息数量
    /// </summary>
    public int RetainedMessages { get; set; }

    /// <summary>
    /// 已发布消息总数
    /// </summary>
    public long PublishedMessages { get; set; }

    /// <summary>
    /// 已接收消息总数
    /// </summary>
    public long ReceivedMessages { get; set; }

    /// <summary>
    /// 服务启动时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 服务运行时间
    /// </summary>
    public TimeSpan Uptime => DateTime.UtcNow - StartTime;
}
