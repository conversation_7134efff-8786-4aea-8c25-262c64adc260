using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace MqttBroker.Web.Controllers;

/// <summary>
/// 健康检查控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly ILogger<HealthController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public HealthController(ILogger<HealthController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取服务健康状态
    /// </summary>
    /// <returns>健康状态信息</returns>
    [HttpGet]
    public IActionResult Get()
    {
        _logger.LogInformation("健康检查请求");
        
        var healthInfo = new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production",
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            WorkingSet = GC.GetTotalMemory(false),
            Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime
        };

        return Ok(healthInfo);
    }

    /// <summary>
    /// 获取详细的系统信息
    /// </summary>
    /// <returns>系统信息</returns>
    [HttpGet("detailed")]
    public IActionResult GetDetailed()
    {
        _logger.LogInformation("详细健康检查请求");
        
        var process = Process.GetCurrentProcess();
        var detailedInfo = new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Application = new
            {
                Name = "MQTT Broker",
                Version = "1.0.0",
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production",
                StartTime = process.StartTime,
                Uptime = DateTime.UtcNow - process.StartTime
            },
            System = new
            {
                MachineName = Environment.MachineName,
                ProcessorCount = Environment.ProcessorCount,
                OSVersion = Environment.OSVersion.ToString(),
                Is64BitOperatingSystem = Environment.Is64BitOperatingSystem,
                Is64BitProcess = Environment.Is64BitProcess
            },
            Memory = new
            {
                WorkingSet = process.WorkingSet64,
                PrivateMemorySize = process.PrivateMemorySize64,
                VirtualMemorySize = process.VirtualMemorySize64,
                GCTotalMemory = GC.GetTotalMemory(false)
            },
            Process = new
            {
                Id = process.Id,
                ProcessName = process.ProcessName,
                ThreadCount = process.Threads.Count,
                HandleCount = process.HandleCount
            }
        };

        return Ok(detailedInfo);
    }
}
