using MqttBroker.Core.Models;

namespace MqttBroker.Core.Interfaces;

/// <summary>
/// 连接管理器接口
/// </summary>
public interface IConnectionManager
{
    /// <summary>
    /// 添加客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="connection">连接对象</param>
    /// <returns>是否添加成功</returns>
    Task<bool> AddConnectionAsync(string clientId, IClientConnection connection);

    /// <summary>
    /// 移除客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否移除成功</returns>
    Task<bool> RemoveConnectionAsync(string clientId);

    /// <summary>
    /// 获取客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>客户端连接</returns>
    Task<IClientConnection?> GetConnectionAsync(string clientId);

    /// <summary>
    /// 获取所有连接的客户端ID
    /// </summary>
    /// <returns>客户端ID列表</returns>
    Task<IEnumerable<string>> GetConnectedClientIdsAsync();

    /// <summary>
    /// 获取连接数量
    /// </summary>
    /// <returns>连接数量</returns>
    Task<int> GetConnectionCountAsync();

    /// <summary>
    /// 检查客户端是否已连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否已连接</returns>
    Task<bool> IsConnectedAsync(string clientId);

    /// <summary>
    /// 断开所有连接
    /// </summary>
    /// <returns>断开连接任务</returns>
    Task DisconnectAllAsync();

    /// <summary>
    /// 断开指定客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="reason">断开原因</param>
    /// <returns>断开连接任务</returns>
    Task DisconnectClientAsync(string clientId, string? reason = null);
}

/// <summary>
/// 客户端连接接口
/// </summary>
public interface IClientConnection : IDisposable
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    string ClientId { get; }

    /// <summary>
    /// 连接状态
    /// </summary>
    ClientConnectionStatus Status { get; }

    /// <summary>
    /// 连接时间
    /// </summary>
    DateTime ConnectedAt { get; }

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    DateTime LastActivity { get; }

    /// <summary>
    /// 协议版本
    /// </summary>
    MqttProtocolVersion ProtocolVersion { get; }

    /// <summary>
    /// 心跳间隔
    /// </summary>
    TimeSpan KeepAlive { get; }

    /// <summary>
    /// 是否清理会话
    /// </summary>
    bool CleanSession { get; }

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    string? IpAddress { get; }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>发送任务</returns>
    Task SendMessageAsync(IMqttMessage message);

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="reason">断开原因</param>
    /// <returns>断开任务</returns>
    Task DisconnectAsync(string? reason = null);

    /// <summary>
    /// 更新最后活跃时间
    /// </summary>
    void UpdateLastActivity();

    /// <summary>
    /// 检查连接是否存活
    /// </summary>
    /// <returns>是否存活</returns>
    bool IsAlive();
}

/// <summary>
/// MQTT消息接口
/// </summary>
public interface IMqttMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    int? MessageId { get; }

    /// <summary>
    /// 主题
    /// </summary>
    string Topic { get; }

    /// <summary>
    /// 负载
    /// </summary>
    byte[] Payload { get; }

    /// <summary>
    /// QoS等级
    /// </summary>
    MqttQoSLevel QoS { get; }

    /// <summary>
    /// 是否保留
    /// </summary>
    bool Retain { get; }

    /// <summary>
    /// 是否重复
    /// </summary>
    bool Duplicate { get; }

    /// <summary>
    /// 时间戳
    /// </summary>
    DateTime Timestamp { get; }

    /// <summary>
    /// 过期时间
    /// </summary>
    DateTime? ExpiryTime { get; }

    /// <summary>
    /// 发布者客户端ID
    /// </summary>
    string? PublisherClientId { get; }
}
