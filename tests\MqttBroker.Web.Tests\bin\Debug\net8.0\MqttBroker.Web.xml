<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MqttBroker.Web</name>
    </assembly>
    <members>
        <member name="T:MqttBroker.Web.Controllers.HealthController">
            <summary>
            健康检查控制器
            </summary>
        </member>
        <member name="M:MqttBroker.Web.Controllers.HealthController.#ctor(Microsoft.Extensions.Logging.ILogger{MqttBroker.Web.Controllers.HealthController})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:MqttBroker.Web.Controllers.HealthController.Get">
            <summary>
            获取服务健康状态
            </summary>
            <returns>健康状态信息</returns>
        </member>
        <member name="M:MqttBroker.Web.Controllers.HealthController.GetDetailed">
            <summary>
            获取详细的系统信息
            </summary>
            <returns>系统信息</returns>
        </member>
        <member name="T:MqttBroker.Web.Program">
            <summary>
            MQTT Broker Web应用程序入口点
            </summary>
        </member>
        <member name="M:MqttBroker.Web.Program.ConfigureServices(Microsoft.AspNetCore.Builder.WebApplicationBuilder)">
            <summary>
            配置服务
            </summary>
        </member>
        <member name="M:MqttBroker.Web.Program.ConfigureMiddleware(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            配置中间件管道
            </summary>
        </member>
        <member name="M:MqttBroker.Web.Program.EnsureDatabaseCreated(Microsoft.AspNetCore.Builder.WebApplication)">
            <summary>
            确保数据库已创建
            </summary>
        </member>
        <member name="M:MqttBroker.Web.Program.GetConfiguration">
            <summary>
            获取配置
            </summary>
        </member>
    </members>
</doc>
