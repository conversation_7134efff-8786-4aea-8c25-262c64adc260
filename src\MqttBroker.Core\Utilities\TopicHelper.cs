using MqttBroker.Core.Protocol;

namespace MqttBroker.Core.Utilities;

/// <summary>
/// 主题处理帮助类
/// </summary>
public static class TopicHelper
{
    /// <summary>
    /// 验证主题名称是否有效
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <returns>是否有效</returns>
    public static bool IsValidTopicName(string? topic)
    {
        if (string.IsNullOrEmpty(topic))
            return false;

        if (topic.Length > MqttProtocolConstants.Defaults.MaxTopicLength)
            return false;

        // 主题名称不能包含通配符
        if (topic.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) ||
            topic.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
            return false;

        // 不能以 $ 开头（系统主题）
        if (topic.StartsWith("$"))
            return false;

        return true;
    }

    /// <summary>
    /// 验证主题过滤器是否有效
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否有效</returns>
    public static bool IsValidTopicFilter(string? topicFilter)
    {
        if (string.IsNullOrEmpty(topicFilter))
            return false;

        if (topicFilter.Length > MqttProtocolConstants.Defaults.MaxTopicLength)
            return false;

        // 检查多级通配符的使用
        var multiLevelIndex = topicFilter.IndexOf(MqttProtocolConstants.TopicWildcards.MultiLevel);
        if (multiLevelIndex != -1)
        {
            // 多级通配符只能出现在末尾
            if (multiLevelIndex != topicFilter.Length - 1)
                return false;

            // 多级通配符前面必须是分隔符或者是开头
            if (multiLevelIndex > 0 && topicFilter[multiLevelIndex - 1] != MqttProtocolConstants.TopicWildcards.Separator)
                return false;
        }

        // 检查单级通配符的使用
        var parts = topicFilter.Split(MqttProtocolConstants.TopicWildcards.Separator);
        foreach (var part in parts)
        {
            if (part.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel))
            {
                // 单级通配符必须独占一个级别
                if (part.Length != 1)
                    return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检查主题是否匹配过滤器
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否匹配</returns>
    public static bool IsTopicMatch(string topic, string topicFilter)
    {
        if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(topicFilter))
            return false;

        // 系统主题只能被系统主题过滤器匹配
        if (topic.StartsWith("$") && !topicFilter.StartsWith("$"))
            return false;

        if (!topic.StartsWith("$") && topicFilter.StartsWith("$"))
            return false;

        // 完全匹配
        if (topic == topicFilter)
            return true;

        // 如果过滤器不包含通配符，则必须完全匹配
        if (!topicFilter.Contains(MqttProtocolConstants.TopicWildcards.SingleLevel) &&
            !topicFilter.Contains(MqttProtocolConstants.TopicWildcards.MultiLevel))
            return false;

        return MatchTopicWithWildcards(topic, topicFilter);
    }

    /// <summary>
    /// 使用通配符匹配主题
    /// </summary>
    /// <param name="topic">主题名称</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否匹配</returns>
    private static bool MatchTopicWithWildcards(string topic, string topicFilter)
    {
        var topicParts = topic.Split(MqttProtocolConstants.TopicWildcards.Separator);
        var filterParts = topicFilter.Split(MqttProtocolConstants.TopicWildcards.Separator);

        return MatchParts(topicParts, filterParts, 0, 0);
    }

    /// <summary>
    /// 递归匹配主题部分
    /// </summary>
    /// <param name="topicParts">主题部分</param>
    /// <param name="filterParts">过滤器部分</param>
    /// <param name="topicIndex">主题索引</param>
    /// <param name="filterIndex">过滤器索引</param>
    /// <returns>是否匹配</returns>
    private static bool MatchParts(string[] topicParts, string[] filterParts, int topicIndex, int filterIndex)
    {
        // 如果过滤器已经处理完
        if (filterIndex >= filterParts.Length)
        {
            // 主题也必须处理完才匹配
            return topicIndex >= topicParts.Length;
        }

        var filterPart = filterParts[filterIndex];

        // 多级通配符
        if (filterPart == MqttProtocolConstants.TopicWildcards.MultiLevel.ToString())
        {
            // 多级通配符匹配剩余所有级别
            return true;
        }

        // 如果主题已经处理完但过滤器还有
        if (topicIndex >= topicParts.Length)
        {
            return false;
        }

        var topicPart = topicParts[topicIndex];

        // 单级通配符或完全匹配
        if (filterPart == MqttProtocolConstants.TopicWildcards.SingleLevel.ToString() || filterPart == topicPart)
        {
            return MatchParts(topicParts, filterParts, topicIndex + 1, filterIndex + 1);
        }

        return false;
    }

    /// <summary>
    /// 获取主题的级别数
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>级别数</returns>
    public static int GetTopicLevels(string topic)
    {
        if (string.IsNullOrEmpty(topic))
            return 0;

        return topic.Split(MqttProtocolConstants.TopicWildcards.Separator).Length;
    }

    /// <summary>
    /// 获取主题的父级主题
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>父级主题，如果没有则返回null</returns>
    public static string? GetParentTopic(string topic)
    {
        if (string.IsNullOrEmpty(topic))
            return null;

        var lastSeparatorIndex = topic.LastIndexOf(MqttProtocolConstants.TopicWildcards.Separator);
        if (lastSeparatorIndex <= 0)
            return null;

        return topic.Substring(0, lastSeparatorIndex);
    }

    /// <summary>
    /// 获取主题的最后一级
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>最后一级</returns>
    public static string GetTopicLastLevel(string topic)
    {
        if (string.IsNullOrEmpty(topic))
            return string.Empty;

        var lastSeparatorIndex = topic.LastIndexOf(MqttProtocolConstants.TopicWildcards.Separator);
        if (lastSeparatorIndex < 0)
            return topic;

        return topic.Substring(lastSeparatorIndex + 1);
    }

    /// <summary>
    /// 标准化主题（移除多余的分隔符）
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>标准化后的主题</returns>
    public static string NormalizeTopic(string topic)
    {
        if (string.IsNullOrEmpty(topic))
            return string.Empty;

        // 移除开头和结尾的分隔符
        topic = topic.Trim(MqttProtocolConstants.TopicWildcards.Separator);

        // 移除连续的分隔符
        while (topic.Contains("//"))
        {
            topic = topic.Replace("//", "/");
        }

        return topic;
    }

    /// <summary>
    /// 检查是否为系统主题
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>是否为系统主题</returns>
    public static bool IsSystemTopic(string topic)
    {
        return !string.IsNullOrEmpty(topic) && topic.StartsWith("$");
    }

    /// <summary>
    /// 检查是否为共享订阅
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否为共享订阅</returns>
    public static bool IsSharedSubscription(string topicFilter)
    {
        return !string.IsNullOrEmpty(topicFilter) && topicFilter.StartsWith("$share/");
    }

    /// <summary>
    /// 解析共享订阅
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>共享组名和实际过滤器</returns>
    public static (string? ShareName, string? ActualFilter) ParseSharedSubscription(string topicFilter)
    {
        if (!IsSharedSubscription(topicFilter))
            return (null, topicFilter);

        var parts = topicFilter.Split(MqttProtocolConstants.TopicWildcards.Separator, 3);
        if (parts.Length < 3)
            return (null, topicFilter);

        return (parts[1], parts[2]);
    }
}
