namespace MqttBroker.Core.Models;

/// <summary>
/// MQTT QoS等级
/// </summary>
public enum MqttQoSLevel : byte
{
    /// <summary>
    /// 最多一次传递
    /// </summary>
    AtMostOnce = 0,

    /// <summary>
    /// 至少一次传递
    /// </summary>
    AtLeastOnce = 1,

    /// <summary>
    /// 恰好一次传递
    /// </summary>
    ExactlyOnce = 2
}

/// <summary>
/// MQTT协议版本
/// </summary>
public enum MqttProtocolVersion : byte
{
    /// <summary>
    /// MQTT 3.1.1
    /// </summary>
    V311 = 4,

    /// <summary>
    /// MQTT 5.0
    /// </summary>
    V50 = 5
}

/// <summary>
/// 客户端连接状态
/// </summary>
public enum ClientConnectionStatus
{
    /// <summary>
    /// 已断开连接
    /// </summary>
    Disconnected,

    /// <summary>
    /// 正在连接
    /// </summary>
    Connecting,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected,

    /// <summary>
    /// 正在断开连接
    /// </summary>
    Disconnecting,

    /// <summary>
    /// 连接失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已禁用
    /// </summary>
    Disabled
}

/// <summary>
/// 消息状态
/// </summary>
public enum MessageStatus
{
    /// <summary>
    /// 已发布
    /// </summary>
    Published,

    /// <summary>
    /// 等待确认
    /// </summary>
    PendingAcknowledgment,

    /// <summary>
    /// 已确认
    /// </summary>
    Acknowledged,

    /// <summary>
    /// 已接收
    /// </summary>
    Received,

    /// <summary>
    /// 已释放
    /// </summary>
    Released,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired,

    /// <summary>
    /// 发送失败
    /// </summary>
    Failed
}

/// <summary>
/// 会话状态
/// </summary>
public enum SessionStatus
{
    /// <summary>
    /// 活跃
    /// </summary>
    Active,

    /// <summary>
    /// 非活跃
    /// </summary>
    Inactive,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired,

    /// <summary>
    /// 已清理
    /// </summary>
    Cleaned
}

/// <summary>
    /// 订阅选项（MQTT 5.0）
    /// </summary>
    [Flags]
    public enum SubscriptionOptions : byte
    {
        /// <summary>
        /// 无选项
        /// </summary>
        None = 0,

        /// <summary>
        /// 不发送本地发布的消息
        /// </summary>
        NoLocal = 1,

        /// <summary>
        /// 保留消息处理 - 发送保留消息
        /// </summary>
        RetainAsPublished = 2,

        /// <summary>
        /// 保留消息处理 - 仅在订阅时发送
        /// </summary>
        RetainHandling_SendOnSubscribe = 0,

        /// <summary>
        /// 保留消息处理 - 仅在新订阅时发送
        /// </summary>
        RetainHandling_SendOnSubscribeIfNew = 16,

        /// <summary>
        /// 保留消息处理 - 不发送
        /// </summary>
        RetainHandling_DoNotSend = 32
    }

/// <summary>
/// 连接返回码
/// </summary>
public enum ConnectReturnCode : byte
{
    /// <summary>
    /// 连接已接受
    /// </summary>
    Accepted = 0,

    /// <summary>
    /// 不可接受的协议版本
    /// </summary>
    UnacceptableProtocolVersion = 1,

    /// <summary>
    /// 标识符被拒绝
    /// </summary>
    IdentifierRejected = 2,

    /// <summary>
    /// 服务器不可用
    /// </summary>
    ServerUnavailable = 3,

    /// <summary>
    /// 错误的用户名或密码
    /// </summary>
    BadUsernameOrPassword = 4,

    /// <summary>
    /// 未授权
    /// </summary>
    NotAuthorized = 5
}

/// <summary>
/// MQTT 5.0 原因码
/// </summary>
public enum ReasonCode : byte
{
    /// <summary>
    /// 成功
    /// </summary>
    Success = 0x00,

    /// <summary>
    /// 正常断开连接
    /// </summary>
    NormalDisconnection = 0x00,

    /// <summary>
    /// 授予的QoS 0
    /// </summary>
    GrantedQoS0 = 0x00,

    /// <summary>
    /// 授予的QoS 1
    /// </summary>
    GrantedQoS1 = 0x01,

    /// <summary>
    /// 授予的QoS 2
    /// </summary>
    GrantedQoS2 = 0x02,

    /// <summary>
    /// 带有遗嘱消息的断开连接
    /// </summary>
    DisconnectWithWillMessage = 0x04,

    /// <summary>
    /// 无匹配的订阅者
    /// </summary>
    NoMatchingSubscribers = 0x10,

    /// <summary>
    /// 无订阅存在
    /// </summary>
    NoSubscriptionExisted = 0x11,

    /// <summary>
    /// 继续认证
    /// </summary>
    ContinueAuthentication = 0x18,

    /// <summary>
    /// 重新认证
    /// </summary>
    ReAuthenticate = 0x19,

    /// <summary>
    /// 未指定错误
    /// </summary>
    UnspecifiedError = 0x80,

    /// <summary>
    /// 格式错误的数据包
    /// </summary>
    MalformedPacket = 0x81,

    /// <summary>
    /// 协议错误
    /// </summary>
    ProtocolError = 0x82,

    /// <summary>
    /// 实现特定错误
    /// </summary>
    ImplementationSpecificError = 0x83,

    /// <summary>
    /// 不支持的协议版本
    /// </summary>
    UnsupportedProtocolVersion = 0x84,

    /// <summary>
    /// 客户端标识符无效
    /// </summary>
    ClientIdentifierNotValid = 0x85,

    /// <summary>
    /// 错误的用户名或密码
    /// </summary>
    BadUserNameOrPassword = 0x86,

    /// <summary>
    /// 未授权
    /// </summary>
    NotAuthorized = 0x87,

    /// <summary>
    /// 服务器不可用
    /// </summary>
    ServerUnavailable = 0x88,

    /// <summary>
    /// 服务器繁忙
    /// </summary>
    ServerBusy = 0x89,

    /// <summary>
    /// 已禁止
    /// </summary>
    Banned = 0x8A,

    /// <summary>
    /// 服务器正在关闭
    /// </summary>
    ServerShuttingDown = 0x8B,

    /// <summary>
    /// 错误的认证方法
    /// </summary>
    BadAuthenticationMethod = 0x8C,

    /// <summary>
    /// 保持活动超时
    /// </summary>
    KeepAliveTimeout = 0x8D,

    /// <summary>
    /// 会话被接管
    /// </summary>
    SessionTakenOver = 0x8E,

    /// <summary>
    /// 主题过滤器无效
    /// </summary>
    TopicFilterInvalid = 0x8F,

    /// <summary>
    /// 主题名称无效
    /// </summary>
    TopicNameInvalid = 0x90,

    /// <summary>
    /// 数据包标识符正在使用
    /// </summary>
    PacketIdentifierInUse = 0x91,

    /// <summary>
    /// 数据包标识符未找到
    /// </summary>
    PacketIdentifierNotFound = 0x92,

    /// <summary>
    /// 接收最大值超出
    /// </summary>
    ReceiveMaximumExceeded = 0x93,

    /// <summary>
    /// 主题别名无效
    /// </summary>
    TopicAliasInvalid = 0x94,

    /// <summary>
    /// 数据包过大
    /// </summary>
    PacketTooLarge = 0x95,

    /// <summary>
    /// 消息速率过高
    /// </summary>
    MessageRateTooHigh = 0x96,

    /// <summary>
    /// 配额超出
    /// </summary>
    QuotaExceeded = 0x97,

    /// <summary>
    /// 管理操作
    /// </summary>
    AdministrativeAction = 0x98,

    /// <summary>
    /// 负载格式无效
    /// </summary>
    PayloadFormatInvalid = 0x99,

    /// <summary>
    /// 不支持保留
    /// </summary>
    RetainNotSupported = 0x9A,

    /// <summary>
    /// 不支持的QoS
    /// </summary>
    QoSNotSupported = 0x9B,

    /// <summary>
    /// 使用另一个服务器
    /// </summary>
    UseAnotherServer = 0x9C,

    /// <summary>
    /// 服务器已移动
    /// </summary>
    ServerMoved = 0x9D,

    /// <summary>
    /// 不支持共享订阅
    /// </summary>
    SharedSubscriptionsNotSupported = 0x9E,

    /// <summary>
    /// 连接速率超出
    /// </summary>
    ConnectionRateExceeded = 0x9F,

    /// <summary>
    /// 最大连接时间
    /// </summary>
    MaximumConnectTime = 0xA0,

    /// <summary>
    /// 不支持订阅标识符
    /// </summary>
    SubscriptionIdentifiersNotSupported = 0xA1,

    /// <summary>
    /// 不支持通配符订阅
    /// </summary>
    WildcardSubscriptionsNotSupported = 0xA2
}
