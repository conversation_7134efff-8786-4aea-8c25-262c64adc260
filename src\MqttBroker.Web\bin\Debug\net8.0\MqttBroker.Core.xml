<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MqttBroker.Core</name>
    </assembly>
    <members>
        <member name="T:MqttBroker.Core.Interfaces.IAuthenticationService">
            <summary>
            认证服务接口
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthenticationService.AuthenticateAsync(MqttBroker.Core.Interfaces.AuthenticationRequest)">
            <summary>
            验证客户端连接
            </summary>
            <param name="request">认证请求</param>
            <returns>认证结果</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthenticationService.ValidateClientIdAsync(System.String)">
            <summary>
            验证客户端ID
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthenticationService.ValidateCredentialsAsync(System.String,System.String)">
            <summary>
            验证用户名和密码
            </summary>
            <param name="username">用户名</param>
            <param name="password">密码</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthenticationService.GenerateClientIdAsync">
            <summary>
            生成客户端ID
            </summary>
            <returns>生成的客户端ID</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthenticationService.IsClientDisabledAsync(System.String)">
            <summary>
            检查客户端是否被禁用
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>是否被禁用</returns>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.IAuthorizationService">
            <summary>
            授权服务接口
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthorizationService.CanPublishAsync(System.String,System.String)">
            <summary>
            检查发布权限
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="topic">主题</param>
            <returns>是否有权限</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthorizationService.CanSubscribeAsync(System.String,System.String)">
            <summary>
            检查订阅权限
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否有权限</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthorizationService.CanConnectAsync(System.String,System.String)">
            <summary>
            检查连接权限
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="ipAddress">IP地址</param>
            <returns>是否有权限</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthorizationService.GetMaxQoSAsync(System.String)">
            <summary>
            获取客户端的最大QoS等级
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>最大QoS等级</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IAuthorizationService.GetPermissionsAsync(System.String)">
            <summary>
            获取客户端的权限列表
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>权限列表</returns>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.AuthenticationRequest">
            <summary>
            认证请求
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.IpAddress">
            <summary>
            客户端IP地址
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.CleanSession">
            <summary>
            是否清理会话
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.KeepAlive">
            <summary>
            心跳间隔
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.WillMessage">
            <summary>
            遗嘱消息
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationRequest.ClientCertificate">
            <summary>
            客户端证书（用于TLS认证）
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.AuthenticationResult">
            <summary>
            认证结果
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.ReturnCode">
            <summary>
            返回码
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.ReasonCode">
            <summary>
            原因码（MQTT 5.0）
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.AssignedClientId">
            <summary>
            分配的客户端ID（如果原始ID为空）
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.SessionPresent">
            <summary>
            会话是否存在
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.ServerKeepAlive">
            <summary>
            服务器保持活跃时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.MaxQoS">
            <summary>
            最大QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.RetainAvailable">
            <summary>
            是否支持保留消息
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.WildcardSubscriptionAvailable">
            <summary>
            是否支持通配符订阅
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.SubscriptionIdentifierAvailable">
            <summary>
            是否支持订阅标识符
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.AuthenticationResult.SharedSubscriptionAvailable">
            <summary>
            是否支持共享订阅
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.WillMessage">
            <summary>
            遗嘱消息
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.WillMessage.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.WillMessage.Payload">
            <summary>
            负载
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.WillMessage.QoS">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.WillMessage.Retain">
            <summary>
            是否保留
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.WillMessage.DelayInterval">
            <summary>
            延迟间隔（MQTT 5.0）
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.Permission">
            <summary>
            权限
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.Permission.Type">
            <summary>
            权限类型
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.Permission.TopicPattern">
            <summary>
            主题模式
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.Permission.Allow">
            <summary>
            是否允许
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.Permission.MaxQoS">
            <summary>
            最大QoS等级
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.PermissionType">
            <summary>
            权限类型
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Interfaces.PermissionType.Publish">
            <summary>
            发布权限
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Interfaces.PermissionType.Subscribe">
            <summary>
            订阅权限
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Interfaces.PermissionType.Connect">
            <summary>
            连接权限
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.IConnectionManager">
            <summary>
            连接管理器接口
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.AddConnectionAsync(System.String,MqttBroker.Core.Interfaces.IClientConnection)">
            <summary>
            添加客户端连接
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="connection">连接对象</param>
            <returns>是否添加成功</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.RemoveConnectionAsync(System.String)">
            <summary>
            移除客户端连接
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>是否移除成功</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.GetConnectionAsync(System.String)">
            <summary>
            获取客户端连接
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>客户端连接</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.GetConnectedClientIdsAsync">
            <summary>
            获取所有连接的客户端ID
            </summary>
            <returns>客户端ID列表</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.GetConnectionCountAsync">
            <summary>
            获取连接数量
            </summary>
            <returns>连接数量</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.IsConnectedAsync(System.String)">
            <summary>
            检查客户端是否已连接
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>是否已连接</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.DisconnectAllAsync">
            <summary>
            断开所有连接
            </summary>
            <returns>断开连接任务</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IConnectionManager.DisconnectClientAsync(System.String,System.String)">
            <summary>
            断开指定客户端连接
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="reason">断开原因</param>
            <returns>断开连接任务</returns>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.IClientConnection">
            <summary>
            客户端连接接口
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.ConnectedAt">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.LastActivity">
            <summary>
            最后活跃时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.KeepAlive">
            <summary>
            心跳间隔
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.CleanSession">
            <summary>
            是否清理会话
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IClientConnection.IpAddress">
            <summary>
            客户端IP地址
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IClientConnection.SendMessageAsync(MqttBroker.Core.Interfaces.IMqttMessage)">
            <summary>
            发送消息
            </summary>
            <param name="message">消息</param>
            <returns>发送任务</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IClientConnection.DisconnectAsync(System.String)">
            <summary>
            断开连接
            </summary>
            <param name="reason">断开原因</param>
            <returns>断开任务</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IClientConnection.UpdateLastActivity">
            <summary>
            更新最后活跃时间
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IClientConnection.IsAlive">
            <summary>
            检查连接是否存活
            </summary>
            <returns>是否存活</returns>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.IMqttMessage">
            <summary>
            MQTT消息接口
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.MessageId">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.Payload">
            <summary>
            负载
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.QoS">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.Retain">
            <summary>
            是否保留
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.Duplicate">
            <summary>
            是否重复
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.ExpiryTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.IMqttMessage.PublisherClientId">
            <summary>
            发布者客户端ID
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.IMessageRouter">
            <summary>
            消息路由器接口
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.RouteMessageAsync(MqttBroker.Core.Interfaces.IMqttMessage)">
            <summary>
            路由消息到订阅者
            </summary>
            <param name="message">要路由的消息</param>
            <returns>路由任务</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.AddSubscriptionAsync(System.String,System.String,MqttBroker.Core.Models.MqttQoSLevel,MqttBroker.Core.Models.SubscriptionOptions)">
            <summary>
            添加订阅
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="topicFilter">主题过滤器</param>
            <param name="qos">QoS等级</param>
            <param name="options">订阅选项</param>
            <returns>是否添加成功</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.RemoveSubscriptionAsync(System.String,System.String)">
            <summary>
            移除订阅
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否移除成功</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.RemoveAllSubscriptionsAsync(System.String)">
            <summary>
            移除客户端的所有订阅
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>移除的订阅数量</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.GetSubscribersAsync(System.String)">
            <summary>
            获取主题的订阅者
            </summary>
            <param name="topic">主题</param>
            <returns>订阅者列表</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.GetClientSubscriptionsAsync(System.String)">
            <summary>
            获取客户端的订阅
            </summary>
            <param name="clientId">客户端ID</param>
            <returns>订阅列表</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.IsTopicMatch(System.String,System.String)">
            <summary>
            检查主题是否匹配过滤器
            </summary>
            <param name="topic">主题</param>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否匹配</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.IsValidTopicFilter(System.String)">
            <summary>
            验证主题过滤器
            </summary>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IMessageRouter.IsValidTopicName(System.String)">
            <summary>
            验证主题名称
            </summary>
            <param name="topic">主题名称</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.ISubscription">
            <summary>
            订阅接口
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.Id">
            <summary>
            订阅ID
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.TopicFilter">
            <summary>
            主题过滤器
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.QoS">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.Options">
            <summary>
            订阅选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.CreatedAt">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.IsActive">
            <summary>
            是否活跃
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.MatchedMessagesCount">
            <summary>
            匹配的消息数量
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.LastMatchedAt">
            <summary>
            最后匹配时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Interfaces.ISubscription.SubscriptionIdentifier">
            <summary>
            订阅标识符（MQTT 5.0）
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Interfaces.IRetainedMessageManager">
            <summary>
            保留消息管理器接口
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IRetainedMessageManager.SetRetainedMessageAsync(System.String,MqttBroker.Core.Interfaces.IMqttMessage)">
            <summary>
            设置保留消息
            </summary>
            <param name="topic">主题</param>
            <param name="message">消息</param>
            <returns>设置任务</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IRetainedMessageManager.GetRetainedMessageAsync(System.String)">
            <summary>
            获取保留消息
            </summary>
            <param name="topic">主题</param>
            <returns>保留消息</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IRetainedMessageManager.DeleteRetainedMessageAsync(System.String)">
            <summary>
            删除保留消息
            </summary>
            <param name="topic">主题</param>
            <returns>是否删除成功</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IRetainedMessageManager.GetRetainedMessagesAsync(System.String)">
            <summary>
            获取匹配主题过滤器的保留消息
            </summary>
            <param name="topicFilter">主题过滤器</param>
            <returns>保留消息列表</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IRetainedMessageManager.CleanupExpiredMessagesAsync">
            <summary>
            清理过期的保留消息
            </summary>
            <returns>清理的消息数量</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IRetainedMessageManager.GetRetainedMessageCountAsync">
            <summary>
            获取保留消息数量
            </summary>
            <returns>消息数量</returns>
        </member>
        <member name="M:MqttBroker.Core.Interfaces.IRetainedMessageManager.GetRetainedTopicsAsync">
            <summary>
            获取所有保留消息的主题
            </summary>
            <returns>主题列表</returns>
        </member>
        <member name="T:MqttBroker.Core.Models.MqttBrokerOptions">
            <summary>
            MQTT Broker 配置选项
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MqttBrokerOptions.SectionName">
            <summary>
            配置节名称
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.MqttBrokerOptions.ServerOptions">
            <summary>
            服务器选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.MqttBrokerOptions.Authentication">
            <summary>
            认证选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.MqttBrokerOptions.Persistence">
            <summary>
            持久化选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.MqttBrokerOptions.Logging">
            <summary>
            日志选项
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.ServerOptions">
            <summary>
            服务器配置选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.Port">
            <summary>
            MQTT端口
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.TlsPort">
            <summary>
            MQTT over TLS端口
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.WebSocketPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.MaxPendingMessagesPerClient">
            <summary>
            每个客户端最大待处理消息数
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.DefaultCommunicationTimeout">
            <summary>
            默认通信超时时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.EnablePersistentSessions">
            <summary>
            是否启用持久会话
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.MaxRetainedMessages">
            <summary>
            最大保留消息数
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.ServerOptions.HeartbeatInterval">
            <summary>
            心跳检查间隔
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.AuthenticationOptions">
            <summary>
            认证配置选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.AuthenticationOptions.AllowAnonymous">
            <summary>
            是否允许匿名连接
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.AuthenticationOptions.RequireClientId">
            <summary>
            是否要求客户端ID
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.AuthenticationOptions.ValidateClientId">
            <summary>
            是否验证客户端ID
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.AuthenticationOptions.DefaultUsername">
            <summary>
            默认用户名
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.AuthenticationOptions.DefaultPassword">
            <summary>
            默认密码
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.AuthenticationOptions.JwtSecret">
            <summary>
            JWT密钥
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.AuthenticationOptions.JwtExpiration">
            <summary>
            JWT过期时间
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.PersistenceOptions">
            <summary>
            持久化配置选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.PersistenceOptions.Provider">
            <summary>
            持久化提供程序
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.PersistenceOptions.RetainedMessagesCount">
            <summary>
            保留消息数量限制
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.PersistenceOptions.QoSMessagesCount">
            <summary>
            QoS消息数量限制
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.PersistenceOptions.CleanupInterval">
            <summary>
            数据清理间隔
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.PersistenceOptions.MessageRetentionTime">
            <summary>
            消息保留时间
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.LoggingOptions">
            <summary>
            日志配置选项
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.LoggingOptions.LogClientConnections">
            <summary>
            是否记录客户端连接
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.LoggingOptions.LogPublishedMessages">
            <summary>
            是否记录发布的消息
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.LoggingOptions.LogSubscriptions">
            <summary>
            是否记录订阅
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.LoggingOptions.LogPerformanceMetrics">
            <summary>
            是否记录性能指标
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Models.LoggingOptions.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.MqttQoSLevel">
            <summary>
            MQTT QoS等级
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MqttQoSLevel.AtMostOnce">
            <summary>
            最多一次传递
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MqttQoSLevel.AtLeastOnce">
            <summary>
            至少一次传递
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MqttQoSLevel.ExactlyOnce">
            <summary>
            恰好一次传递
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.MqttProtocolVersion">
            <summary>
            MQTT协议版本
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MqttProtocolVersion.V311">
            <summary>
            MQTT 3.1.1
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MqttProtocolVersion.V50">
            <summary>
            MQTT 5.0
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.ClientConnectionStatus">
            <summary>
            客户端连接状态
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ClientConnectionStatus.Disconnected">
            <summary>
            已断开连接
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ClientConnectionStatus.Connecting">
            <summary>
            正在连接
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ClientConnectionStatus.Connected">
            <summary>
            已连接
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ClientConnectionStatus.Disconnecting">
            <summary>
            正在断开连接
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ClientConnectionStatus.Failed">
            <summary>
            连接失败
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ClientConnectionStatus.Disabled">
            <summary>
            已禁用
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.MessageStatus">
            <summary>
            消息状态
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.Published">
            <summary>
            已发布
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.PendingAcknowledgment">
            <summary>
            等待确认
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.Acknowledged">
            <summary>
            已确认
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.Received">
            <summary>
            已接收
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.Released">
            <summary>
            已释放
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.Completed">
            <summary>
            已完成
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.Expired">
            <summary>
            已过期
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.MessageStatus.Failed">
            <summary>
            发送失败
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.SessionStatus">
            <summary>
            会话状态
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SessionStatus.Active">
            <summary>
            活跃
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SessionStatus.Inactive">
            <summary>
            非活跃
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SessionStatus.Expired">
            <summary>
            已过期
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SessionStatus.Cleaned">
            <summary>
            已清理
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.SubscriptionOptions">
            <summary>
            订阅选项（MQTT 5.0）
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SubscriptionOptions.None">
            <summary>
            无选项
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SubscriptionOptions.NoLocal">
            <summary>
            不发送本地发布的消息
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SubscriptionOptions.RetainAsPublished">
            <summary>
            保留消息处理 - 发送保留消息
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SubscriptionOptions.RetainHandling_SendOnSubscribe">
            <summary>
            保留消息处理 - 仅在订阅时发送
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SubscriptionOptions.RetainHandling_SendOnSubscribeIfNew">
            <summary>
            保留消息处理 - 仅在新订阅时发送
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.SubscriptionOptions.RetainHandling_DoNotSend">
            <summary>
            保留消息处理 - 不发送
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.ConnectReturnCode">
            <summary>
            连接返回码
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ConnectReturnCode.Accepted">
            <summary>
            连接已接受
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ConnectReturnCode.UnacceptableProtocolVersion">
            <summary>
            不可接受的协议版本
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ConnectReturnCode.IdentifierRejected">
            <summary>
            标识符被拒绝
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ConnectReturnCode.ServerUnavailable">
            <summary>
            服务器不可用
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ConnectReturnCode.BadUsernameOrPassword">
            <summary>
            错误的用户名或密码
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ConnectReturnCode.NotAuthorized">
            <summary>
            未授权
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Models.ReasonCode">
            <summary>
            MQTT 5.0 原因码
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.Success">
            <summary>
            成功
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.NormalDisconnection">
            <summary>
            正常断开连接
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.GrantedQoS0">
            <summary>
            授予的QoS 0
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.GrantedQoS1">
            <summary>
            授予的QoS 1
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.GrantedQoS2">
            <summary>
            授予的QoS 2
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.DisconnectWithWillMessage">
            <summary>
            带有遗嘱消息的断开连接
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.NoMatchingSubscribers">
            <summary>
            无匹配的订阅者
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.NoSubscriptionExisted">
            <summary>
            无订阅存在
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ContinueAuthentication">
            <summary>
            继续认证
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ReAuthenticate">
            <summary>
            重新认证
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.UnspecifiedError">
            <summary>
            未指定错误
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.MalformedPacket">
            <summary>
            格式错误的数据包
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ProtocolError">
            <summary>
            协议错误
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ImplementationSpecificError">
            <summary>
            实现特定错误
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.UnsupportedProtocolVersion">
            <summary>
            不支持的协议版本
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ClientIdentifierNotValid">
            <summary>
            客户端标识符无效
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.BadUserNameOrPassword">
            <summary>
            错误的用户名或密码
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.NotAuthorized">
            <summary>
            未授权
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ServerUnavailable">
            <summary>
            服务器不可用
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ServerBusy">
            <summary>
            服务器繁忙
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.Banned">
            <summary>
            已禁止
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ServerShuttingDown">
            <summary>
            服务器正在关闭
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.BadAuthenticationMethod">
            <summary>
            错误的认证方法
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.KeepAliveTimeout">
            <summary>
            保持活动超时
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.SessionTakenOver">
            <summary>
            会话被接管
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.TopicFilterInvalid">
            <summary>
            主题过滤器无效
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.TopicNameInvalid">
            <summary>
            主题名称无效
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.PacketIdentifierInUse">
            <summary>
            数据包标识符正在使用
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.PacketIdentifierNotFound">
            <summary>
            数据包标识符未找到
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ReceiveMaximumExceeded">
            <summary>
            接收最大值超出
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.TopicAliasInvalid">
            <summary>
            主题别名无效
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.PacketTooLarge">
            <summary>
            数据包过大
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.MessageRateTooHigh">
            <summary>
            消息速率过高
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.QuotaExceeded">
            <summary>
            配额超出
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.AdministrativeAction">
            <summary>
            管理操作
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.PayloadFormatInvalid">
            <summary>
            负载格式无效
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.RetainNotSupported">
            <summary>
            不支持保留
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.QoSNotSupported">
            <summary>
            不支持的QoS
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.UseAnotherServer">
            <summary>
            使用另一个服务器
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ServerMoved">
            <summary>
            服务器已移动
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.SharedSubscriptionsNotSupported">
            <summary>
            不支持共享订阅
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.ConnectionRateExceeded">
            <summary>
            连接速率超出
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.MaximumConnectTime">
            <summary>
            最大连接时间
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.SubscriptionIdentifiersNotSupported">
            <summary>
            不支持订阅标识符
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Models.ReasonCode.WildcardSubscriptionsNotSupported">
            <summary>
            不支持通配符订阅
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants">
            <summary>
            MQTT协议常量
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants.ProtocolVersions">
            <summary>
            MQTT协议版本
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ProtocolVersions.V311">
            <summary>
            MQTT 3.1.1
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ProtocolVersions.V50">
            <summary>
            MQTT 5.0
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes">
            <summary>
            MQTT消息类型
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.CONNECT">
            <summary>
            连接请求
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.CONNACK">
            <summary>
            连接确认
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.PUBLISH">
            <summary>
            发布消息
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.PUBACK">
            <summary>
            发布确认
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.PUBREC">
            <summary>
            发布收到
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.PUBREL">
            <summary>
            发布释放
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.PUBCOMP">
            <summary>
            发布完成
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.SUBSCRIBE">
            <summary>
            订阅请求
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.SUBACK">
            <summary>
            订阅确认
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.UNSUBSCRIBE">
            <summary>
            取消订阅
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.UNSUBACK">
            <summary>
            取消订阅确认
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.PINGREQ">
            <summary>
            心跳请求
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.PINGRESP">
            <summary>
            心跳响应
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.DISCONNECT">
            <summary>
            断开连接
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.MessageTypes.AUTH">
            <summary>
            认证（MQTT 5.0）
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants.QoSLevels">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.QoSLevels.AtMostOnce">
            <summary>
            最多一次
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.QoSLevels.AtLeastOnce">
            <summary>
            至少一次
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.QoSLevels.ExactlyOnce">
            <summary>
            恰好一次
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants.ConnectReturnCodes">
            <summary>
            连接返回码
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ConnectReturnCodes.Accepted">
            <summary>
            连接已接受
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ConnectReturnCodes.UnacceptableProtocolVersion">
            <summary>
            不可接受的协议版本
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ConnectReturnCodes.IdentifierRejected">
            <summary>
            标识符被拒绝
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ConnectReturnCodes.ServerUnavailable">
            <summary>
            服务器不可用
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ConnectReturnCodes.BadUsernameOrPassword">
            <summary>
            错误的用户名或密码
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.ConnectReturnCodes.NotAuthorized">
            <summary>
            未授权
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants.TopicWildcards">
            <summary>
            主题通配符
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.TopicWildcards.SingleLevel">
            <summary>
            单级通配符
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.TopicWildcards.MultiLevel">
            <summary>
            多级通配符
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.TopicWildcards.Separator">
            <summary>
            主题分隔符
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults">
            <summary>
            默认值
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults.Port">
            <summary>
            默认端口
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults.TlsPort">
            <summary>
            默认TLS端口
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults.WebSocketPort">
            <summary>
            默认WebSocket端口
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults.KeepAlive">
            <summary>
            默认心跳间隔（秒）
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults.MaxClientIdLength">
            <summary>
            最大客户端ID长度
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults.MaxTopicLength">
            <summary>
            最大主题长度
            </summary>
        </member>
        <member name="F:MqttBroker.Core.Protocol.MqttProtocolConstants.Defaults.MaxPayloadSize">
            <summary>
            最大负载大小（字节）
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Protocol.MqttProtocolConstants.PropertyIds">
            <summary>
            MQTT 5.0 属性标识符
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Services.IMqttBrokerService">
            <summary>
            MQTT Broker 核心服务接口
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Services.IMqttBrokerService.StartAsync(System.Threading.CancellationToken)">
            <summary>
            启动MQTT Broker服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>启动任务</returns>
        </member>
        <member name="M:MqttBroker.Core.Services.IMqttBrokerService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止MQTT Broker服务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>停止任务</returns>
        </member>
        <member name="P:MqttBroker.Core.Services.IMqttBrokerService.IsRunning">
            <summary>
            获取服务状态
            </summary>
            <returns>服务是否正在运行</returns>
        </member>
        <member name="P:MqttBroker.Core.Services.IMqttBrokerService.ConnectedClientsCount">
            <summary>
            获取连接的客户端数量
            </summary>
            <returns>客户端数量</returns>
        </member>
        <member name="M:MqttBroker.Core.Services.IMqttBrokerService.GetStatisticsAsync">
            <summary>
            获取服务统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <member name="T:MqttBroker.Core.Services.BrokerStatistics">
            <summary>
            Broker统计信息
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Services.BrokerStatistics.ConnectedClients">
            <summary>
            连接的客户端数量
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Services.BrokerStatistics.TotalSubscriptions">
            <summary>
            总订阅数量
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Services.BrokerStatistics.RetainedMessages">
            <summary>
            保留消息数量
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Services.BrokerStatistics.PublishedMessages">
            <summary>
            已发布消息总数
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Services.BrokerStatistics.ReceivedMessages">
            <summary>
            已接收消息总数
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Services.BrokerStatistics.StartTime">
            <summary>
            服务启动时间
            </summary>
        </member>
        <member name="P:MqttBroker.Core.Services.BrokerStatistics.Uptime">
            <summary>
            服务运行时间
            </summary>
        </member>
        <member name="T:MqttBroker.Core.Utilities.TopicHelper">
            <summary>
            主题处理帮助类
            </summary>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.IsValidTopicName(System.String)">
            <summary>
            验证主题名称是否有效
            </summary>
            <param name="topic">主题名称</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.IsValidTopicFilter(System.String)">
            <summary>
            验证主题过滤器是否有效
            </summary>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.IsTopicMatch(System.String,System.String)">
            <summary>
            检查主题是否匹配过滤器
            </summary>
            <param name="topic">主题名称</param>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否匹配</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.MatchTopicWithWildcards(System.String,System.String)">
            <summary>
            使用通配符匹配主题
            </summary>
            <param name="topic">主题名称</param>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否匹配</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.MatchParts(System.String[],System.String[],System.Int32,System.Int32)">
            <summary>
            递归匹配主题部分
            </summary>
            <param name="topicParts">主题部分</param>
            <param name="filterParts">过滤器部分</param>
            <param name="topicIndex">主题索引</param>
            <param name="filterIndex">过滤器索引</param>
            <returns>是否匹配</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.GetTopicLevels(System.String)">
            <summary>
            获取主题的级别数
            </summary>
            <param name="topic">主题</param>
            <returns>级别数</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.GetParentTopic(System.String)">
            <summary>
            获取主题的父级主题
            </summary>
            <param name="topic">主题</param>
            <returns>父级主题，如果没有则返回null</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.GetTopicLastLevel(System.String)">
            <summary>
            获取主题的最后一级
            </summary>
            <param name="topic">主题</param>
            <returns>最后一级</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.NormalizeTopic(System.String)">
            <summary>
            标准化主题（移除多余的分隔符）
            </summary>
            <param name="topic">主题</param>
            <returns>标准化后的主题</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.IsSystemTopic(System.String)">
            <summary>
            检查是否为系统主题
            </summary>
            <param name="topic">主题</param>
            <returns>是否为系统主题</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.IsSharedSubscription(System.String)">
            <summary>
            检查是否为共享订阅
            </summary>
            <param name="topicFilter">主题过滤器</param>
            <returns>是否为共享订阅</returns>
        </member>
        <member name="M:MqttBroker.Core.Utilities.TopicHelper.ParseSharedSubscription(System.String)">
            <summary>
            解析共享订阅
            </summary>
            <param name="topicFilter">主题过滤器</param>
            <returns>共享组名和实际过滤器</returns>
        </member>
    </members>
</doc>
