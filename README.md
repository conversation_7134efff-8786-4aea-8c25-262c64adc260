# MQTT Broker 服务器应用程序

## 项目概括
本项目旨在开发一个基于 .NET 8.0+ 的功能完整的 MQTT Broker 服务器应用程序，支持 MQTT 3.1.1 和 MQTT 5.0 协议标准，提供高性能的消息发布订阅服务，适用于物联网设备通信和实时消息传递场景。

## 技术选型
- **主要编程语言**: C# (.NET 8.0+)
- **Web 框架**: ASP.NET Core (兼容 IIS 部署)
- **网络通信**: ASP.NET Core SignalR + TCP Socket (混合模式)
- **IIS 集成**: ASP.NET Core Module (ANCM)
- **异步编程**: async/await 模式
- **消息持久化**: Entity Framework Core + SQL Server
- **配置管理**: Microsoft.Extensions.Configuration (appsettings.json + IIS 配置)
- **日志记录**: Microsoft.Extensions.Logging + Serilog + IIS 日志集成
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **测试框架**: xUnit + Moq
- **性能监控**: System.Diagnostics.Metrics + IIS 性能计数器
- **部署目标**: IIS 10.0+ (Windows Server 2016+)
- **版本控制**: Git

## 项目结构 / 模块划分
```
/MqttBroker/
├── /src/
│   ├── /MqttBroker.Core/              # 核心业务逻辑
│   │   ├── /Protocol/                 # MQTT 协议解析
│   │   ├── /Services/                 # 核心服务
│   │   ├── /Models/                   # 数据模型
│   │   ├── /Interfaces/               # 接口定义
│   │   └── /Utilities/                # 工具类
│   ├── /MqttBroker.Web/               # ASP.NET Core Web 应用 (IIS 部署)
│   │   ├── /Controllers/              # API 控制器
│   │   ├── /Hubs/                     # SignalR Hubs (WebSocket 支持)
│   │   ├── /Middleware/               # 中间件
│   │   ├── /Services/                 # Web 服务层
│   │   ├── /Configuration/            # 配置管理
│   │   ├── web.config                 # IIS 配置文件
│   │   └── Program.cs                 # 应用程序入口
│   ├── /MqttBroker.Data/              # 数据访问层
│   │   ├── /Entities/                 # 数据实体
│   │   ├── /Repositories/             # 数据仓储
│   │   └── /Context/                  # 数据库上下文
│   └── /MqttBroker.Console/           # 控制台演示程序
├── /tests/
│   ├── /MqttBroker.Core.Tests/        # 核心逻辑测试
│   ├── /MqttBroker.Server.Tests/      # 服务器测试
│   └── /MqttBroker.Integration.Tests/ # 集成测试
├── /docs/                             # 文档目录
├── MqttBroker.sln                     # 解决方案文件
├── appsettings.json                   # 配置文件
└── .gitignore                         # Git 忽略配置
```

## 核心功能 / 模块详解
- **连接管理模块 (ConnectionManager)**: 处理客户端连接、断开连接、心跳检测，维护活跃连接池，支持连接状态监控和超时处理。
- **协议解析模块 (ProtocolHandler)**: 实现 MQTT 3.1.1 和 5.0 协议的数据包解析和构建，包括 CONNECT、PUBLISH、SUBSCRIBE 等消息类型的处理。
- **消息路由模块 (MessageRouter)**: 实现发布订阅消息路由，支持主题过滤器和通配符匹配（+ 单级通配符，# 多级通配符）。
- **QoS 处理模块 (QoSManager)**: 实现 QoS 0（最多一次）、QoS 1（至少一次）、QoS 2（恰好一次）三个服务质量等级的消息传递保证。
- **会话管理模块 (SessionManager)**: 管理客户端会话状态，支持持久会话和清理会话，处理离线消息存储和恢复。
- **认证授权模块 (AuthenticationService)**: 实现基于用户名密码的客户端认证，支持访问控制列表（ACL）和主题权限管理。
- **消息持久化模块 (PersistenceService)**: 负责重要消息、会话状态、订阅信息的数据库存储和恢复，确保服务重启后数据不丢失。
- **监控统计模块 (MetricsService)**: 收集和报告 Broker 运行指标，包括连接数、消息吞吐量、错误率等性能数据。

## 数据模型
- **Client**: { ClientId (PK), Username, Password, IsConnected, LastSeen, CleanSession, KeepAlive }
- **Session**: { SessionId (PK), ClientId (FK), IsPersistent, CreatedAt, LastActivity }
- **Subscription**: { Id (PK), ClientId (FK), TopicFilter, QoS, CreatedAt }
- **Message**: { Id (PK), Topic, Payload, QoS, Retain, Timestamp, ExpiryInterval }
- **RetainedMessage**: { Topic (PK), Payload, QoS, Timestamp }

## 技术实现细节

### 项目结构初始化 {#project-structure-init}

**实现时间**: 2024-01-15

**技术方案**:
- 创建了完整的解决方案结构，包含4个主要项目和3个测试项目
- 配置了.NET 8.0作为目标框架，启用了nullable引用类型和隐式using
- 设置了Entity Framework Core作为数据访问层，支持SQL Server和SQLite
- 配置了Serilog作为日志框架，支持控制台和文件输出
- 集成了ASP.NET Core SignalR用于WebSocket支持
- 配置了xUnit + Moq + FluentAssertions作为测试框架

**关键组件**:
1. **MqttBroker.Core**: 核心业务逻辑库，包含协议解析、服务接口等
2. **MqttBroker.Web**: ASP.NET Core Web应用，支持IIS部署和SignalR
3. **MqttBroker.Data**: 数据访问层，包含EF Core上下文和实体模型
4. **MqttBroker.Console**: 控制台演示程序，用于测试和演示功能

**数据模型设计**:
- Client: 客户端信息，支持连接状态跟踪和认证
- Session: 会话管理，支持持久会话和清理会话
- Subscription: 订阅信息，支持主题过滤器和QoS等级
- Message: 消息存储，支持QoS处理和消息属性
- RetainedMessage: 保留消息，支持主题级别的消息保留

**IIS部署配置**:
- 配置了web.config文件，支持ASP.NET Core Module v2
- 启用了WebSocket支持和安全头设置
- 配置了请求过滤和压缩优化
- 设置了环境变量和日志输出路径

**配置管理**:
- 统一的appsettings.json配置文件结构
- 支持开发、测试、生产环境的配置分离
- 集成了Serilog配置和数据库连接字符串管理

**核心接口设计**:
- IMqttBrokerService: 核心Broker服务接口，提供启动/停止和统计功能
- IConnectionManager: 连接管理接口，处理客户端连接生命周期
- IMessageRouter: 消息路由接口，实现发布订阅模式和主题匹配
- IAuthenticationService: 认证服务接口，支持多种认证方式
- IAuthorizationService: 授权服务接口，实现细粒度权限控制
- IRetainedMessageManager: 保留消息管理接口

**协议支持**:
- 完整的MQTT 3.1.1和5.0协议常量定义
- 支持所有QoS等级（0, 1, 2）
- 完整的消息类型和返回码定义
- MQTT 5.0属性和原因码支持

**工具类库**:
- TopicHelper: 主题处理工具，支持通配符匹配、主题验证、共享订阅解析
- 完整的单元测试覆盖（58个测试用例全部通过）

**测试框架**:
- xUnit + Moq + FluentAssertions 测试技术栈
- 分层测试结构：单元测试、集成测试、Web测试
- 测试覆盖率监控和代码质量保证

## 开发状态跟踪
| 模块/功能                | 状态     | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|--------------------------|----------|--------|--------------|--------------|------------|
| 项目结构初始化           | 已完成   | AI     | 2024-01-15   | 2024-01-15   | [详情](#project-structure-init) |
| 协议解析模块             | 未开始   | AI     | 2024-01-18   |              |            |
| 连接管理模块             | 未开始   | AI     | 2024-01-20   |              |            |
| 消息路由模块             | 未开始   | AI     | 2024-01-22   |              |            |
| QoS 处理模块             | 未开始   | AI     | 2024-01-25   |              |            |
| 会话管理模块             | 未开始   | AI     | 2024-01-27   |              |            |
| 认证授权模块             | 未开始   | AI     | 2024-01-29   |              |            |
| 消息持久化模块           | 未开始   | AI     | 2024-02-01   |              |            |
| 监控统计模块             | 未开始   | AI     | 2024-02-03   |              |            |
| 控制台演示程序           | 未开始   | AI     | 2024-02-05   |              |            |
| 单元测试                 | 未开始   | AI     | 2024-02-08   |              |            |
| 集成测试                 | 未开始   | AI     | 2024-02-10   |              |            |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案。]

## 环境设置与运行指南
### 开发环境要求
- .NET 8.0 SDK 或更高版本
- Visual Studio 2022 或 Visual Studio Code
- SQL Server LocalDB 或 SQLite（用于开发测试）

### 运行步骤
1. 克隆项目到本地
2. 恢复 NuGet 包：`dotnet restore`
3. 更新数据库：`dotnet ef database update`
4. 运行服务器：`dotnet run --project src/MqttBroker.Server`
5. 运行控制台演示：`dotnet run --project src/MqttBroker.Console`

### 测试运行
- 运行所有测试：`dotnet test`
- 运行特定测试项目：`dotnet test tests/MqttBroker.Core.Tests`

## 性能目标
- 支持并发连接数：10,000+
- 消息吞吐量：100,000 消息/秒
- 内存使用：< 1GB（10,000 连接）
- 响应延迟：< 10ms（局域网环境）

## IIS 部署指南

### IIS 部署前提条件
- Windows Server 2016+ 或 Windows 10+
- IIS 10.0+ 已安装并启用
- ASP.NET Core Runtime 8.0+ 已安装
- ASP.NET Core Module (ANCM) v2 已安装

### IIS 部署步骤
1. **发布应用程序**
   ```bash
   dotnet publish src/MqttBroker.Web -c Release -o ./publish
   ```

2. **配置 IIS 应用程序池**
   - 创建新的应用程序池（如：MqttBrokerPool）
   - 设置 .NET CLR 版本为"无托管代码"
   - 设置进程模型标识为 ApplicationPoolIdentity
   - 启用 32 位应用程序：False

3. **创建 IIS 网站**
   - 在 IIS 管理器中创建新网站
   - 设置物理路径指向发布目录
   - 绑定端口（建议使用 8883 用于 MQTT over WebSocket）
   - 选择创建的应用程序池

4. **配置 web.config**
   - 确保 web.config 包含正确的 ASP.NET Core Module 配置
   - 配置环境变量和连接字符串
   - 设置日志路径和级别

5. **SSL/TLS 配置**（推荐用于生产环境）
   - 安装 SSL 证书
   - 配置 HTTPS 绑定
   - 启用 HTTP 到 HTTPS 重定向

### IIS 特殊配置注意事项
- **WebSocket 支持**：确保 IIS 启用了 WebSocket 协议功能
- **长连接处理**：配置适当的超时设置以支持 MQTT 长连接
- **性能优化**：调整应用程序池的回收设置和内存限制
- **监控集成**：配置 IIS 日志和性能计数器集成

### 故障排除
- 检查 ASP.NET Core Module 日志
- 验证应用程序池权限
- 确认防火墙端口开放
- 监控 Windows 事件日志
