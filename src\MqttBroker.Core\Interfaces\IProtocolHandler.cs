using MqttBroker.Core.Models;
using MqttBroker.Core.Protocol;

namespace MqttBroker.Core.Interfaces;

/// <summary>
/// MQTT协议处理器接口
/// </summary>
public interface IProtocolHandler
{
    /// <summary>
    /// 解析MQTT数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <returns>解析结果</returns>
    Task<MqttPacketParseResult> ParsePacketAsync(byte[] buffer, int offset, int length, MqttProtocolVersion protocolVersion = MqttProtocolVersion.V311);

    /// <summary>
    /// 构建MQTT数据包
    /// </summary>
    /// <param name="packet">数据包对象</param>
    /// <returns>序列化后的字节数组</returns>
    Task<byte[]> BuildPacketAsync(MqttPacket packet);

    /// <summary>
    /// 验证数据包完整性
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="availableLength">可用数据长度</param>
    /// <returns>数据包验证结果</returns>
    PacketValidationResult ValidatePacket(byte[] buffer, int offset, int availableLength);

    /// <summary>
    /// 获取数据包类型
    /// </summary>
    /// <param name="firstByte">数据包第一个字节</param>
    /// <returns>消息类型</returns>
    byte GetMessageType(byte firstByte);

    /// <summary>
    /// 计算剩余长度
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <returns>剩余长度计算结果</returns>
    RemainingLengthResult CalculateRemainingLength(byte[] buffer, int offset);

    /// <summary>
    /// 支持的协议版本
    /// </summary>
    IEnumerable<MqttProtocolVersion> SupportedVersions { get; }
}

/// <summary>
/// MQTT数据包解析器接口
/// </summary>
public interface IMqttPacketParser
{
    /// <summary>
    /// 解析CONNECT数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>CONNECT数据包</returns>
    ConnectPacket? ParseConnect(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析CONNACK数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>CONNACK数据包</returns>
    ConnackPacket? ParseConnack(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析PUBLISH数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>PUBLISH数据包</returns>
    PublishPacket? ParsePublish(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析PUBACK数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>PUBACK数据包</returns>
    PubackPacket? ParsePuback(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析SUBSCRIBE数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>SUBSCRIBE数据包</returns>
    SubscribePacket? ParseSubscribe(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析SUBACK数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>SUBACK数据包</returns>
    SubackPacket? ParseSuback(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析UNSUBSCRIBE数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>UNSUBSCRIBE数据包</returns>
    UnsubscribePacket? ParseUnsubscribe(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析UNSUBACK数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>UNSUBACK数据包</returns>
    UnsubackPacket? ParseUnsuback(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析PINGREQ数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>PINGREQ数据包</returns>
    PingreqPacket? ParsePingreq(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析PINGRESP数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>PINGRESP数据包</returns>
    PingrespPacket? ParsePingresp(byte[] buffer, int offset, int length);

    /// <summary>
    /// 解析DISCONNECT数据包
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>DISCONNECT数据包</returns>
    DisconnectPacket? ParseDisconnect(byte[] buffer, int offset, int length);
}

/// <summary>
/// MQTT数据包构建器接口
/// </summary>
public interface IMqttPacketBuilder
{
    /// <summary>
    /// 构建CONNACK数据包
    /// </summary>
    /// <param name="sessionPresent">会话存在标志</param>
    /// <param name="returnCode">返回码</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="properties">属性（MQTT 5.0）</param>
    /// <returns>CONNACK数据包</returns>
    ConnackPacket BuildConnack(bool sessionPresent, ConnectReturnCode returnCode, 
        MqttProtocolVersion protocolVersion = MqttProtocolVersion.V311, 
        Dictionary<byte, object>? properties = null);

    /// <summary>
    /// 构建PUBLISH数据包
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="payload">负载</param>
    /// <param name="qos">QoS等级</param>
    /// <param name="retain">保留标志</param>
    /// <param name="duplicate">重复标志</param>
    /// <param name="packetIdentifier">数据包标识符</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="properties">属性（MQTT 5.0）</param>
    /// <returns>PUBLISH数据包</returns>
    PublishPacket BuildPublish(string topic, byte[] payload, MqttQoSLevel qos = MqttQoSLevel.AtMostOnce,
        bool retain = false, bool duplicate = false, int? packetIdentifier = null,
        MqttProtocolVersion protocolVersion = MqttProtocolVersion.V311,
        Dictionary<byte, object>? properties = null);

    /// <summary>
    /// 构建PUBACK数据包
    /// </summary>
    /// <param name="packetIdentifier">数据包标识符</param>
    /// <param name="reasonCode">原因码（MQTT 5.0）</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="properties">属性（MQTT 5.0）</param>
    /// <returns>PUBACK数据包</returns>
    PubackPacket BuildPuback(int packetIdentifier, ReasonCode reasonCode = ReasonCode.Success,
        MqttProtocolVersion protocolVersion = MqttProtocolVersion.V311,
        Dictionary<byte, object>? properties = null);

    /// <summary>
    /// 构建SUBACK数据包
    /// </summary>
    /// <param name="packetIdentifier">数据包标识符</param>
    /// <param name="returnCodes">返回码列表</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="properties">属性（MQTT 5.0）</param>
    /// <returns>SUBACK数据包</returns>
    SubackPacket BuildSuback(int packetIdentifier, IEnumerable<byte> returnCodes,
        MqttProtocolVersion protocolVersion = MqttProtocolVersion.V311,
        Dictionary<byte, object>? properties = null);

    /// <summary>
    /// 构建UNSUBACK数据包
    /// </summary>
    /// <param name="packetIdentifier">数据包标识符</param>
    /// <param name="reasonCodes">原因码列表（MQTT 5.0）</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="properties">属性（MQTT 5.0）</param>
    /// <returns>UNSUBACK数据包</returns>
    UnsubackPacket BuildUnsuback(int packetIdentifier, IEnumerable<ReasonCode>? reasonCodes = null,
        MqttProtocolVersion protocolVersion = MqttProtocolVersion.V311,
        Dictionary<byte, object>? properties = null);

    /// <summary>
    /// 构建PINGRESP数据包
    /// </summary>
    /// <returns>PINGRESP数据包</returns>
    PingrespPacket BuildPingresp();

    /// <summary>
    /// 构建DISCONNECT数据包
    /// </summary>
    /// <param name="reasonCode">原因码（MQTT 5.0）</param>
    /// <param name="protocolVersion">协议版本</param>
    /// <param name="properties">属性（MQTT 5.0）</param>
    /// <returns>DISCONNECT数据包</returns>
    DisconnectPacket BuildDisconnect(ReasonCode reasonCode = ReasonCode.NormalDisconnection,
        MqttProtocolVersion protocolVersion = MqttProtocolVersion.V311,
        Dictionary<byte, object>? properties = null);
}

/// <summary>
/// 数据包解析结果
/// </summary>
public class MqttPacketParseResult
{
    /// <summary>
    /// 是否解析成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 解析的数据包
    /// </summary>
    public MqttPacket? Packet { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 消耗的字节数
    /// </summary>
    public int ConsumedBytes { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="packet">数据包</param>
    /// <param name="consumedBytes">消耗的字节数</param>
    /// <returns>解析结果</returns>
    public static MqttPacketParseResult Success(MqttPacket packet, int consumedBytes)
    {
        return new MqttPacketParseResult
        {
            IsSuccess = true,
            Packet = packet,
            ConsumedBytes = consumedBytes
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误信息</param>
    /// <returns>解析结果</returns>
    public static MqttPacketParseResult Failure(string errorMessage)
    {
        return new MqttPacketParseResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 数据包验证结果
/// </summary>
public class PacketValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 是否完整（有足够的数据）
    /// </summary>
    public bool IsComplete { get; set; }

    /// <summary>
    /// 需要的总字节数
    /// </summary>
    public int RequiredBytes { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建有效且完整的结果
    /// </summary>
    /// <param name="requiredBytes">需要的字节数</param>
    /// <returns>验证结果</returns>
    public static PacketValidationResult ValidAndComplete(int requiredBytes)
    {
        return new PacketValidationResult
        {
            IsValid = true,
            IsComplete = true,
            RequiredBytes = requiredBytes
        };
    }

    /// <summary>
    /// 创建有效但不完整的结果
    /// </summary>
    /// <param name="requiredBytes">需要的字节数</param>
    /// <returns>验证结果</returns>
    public static PacketValidationResult ValidButIncomplete(int requiredBytes)
    {
        return new PacketValidationResult
        {
            IsValid = true,
            IsComplete = false,
            RequiredBytes = requiredBytes
        };
    }

    /// <summary>
    /// 创建无效结果
    /// </summary>
    /// <param name="errorMessage">错误信息</param>
    /// <returns>验证结果</returns>
    public static PacketValidationResult Invalid(string errorMessage)
    {
        return new PacketValidationResult
        {
            IsValid = false,
            IsComplete = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 剩余长度计算结果
/// </summary>
public class RemainingLengthResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 剩余长度值
    /// </summary>
    public int Value { get; set; }

    /// <summary>
    /// 剩余长度字段占用的字节数
    /// </summary>
    public int BytesConsumed { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="value">剩余长度值</param>
    /// <param name="bytesConsumed">消耗的字节数</param>
    /// <returns>计算结果</returns>
    public static RemainingLengthResult Success(int value, int bytesConsumed)
    {
        return new RemainingLengthResult
        {
            IsSuccess = true,
            Value = value,
            BytesConsumed = bytesConsumed
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误信息</param>
    /// <returns>计算结果</returns>
    public static RemainingLengthResult Failure(string errorMessage)
    {
        return new RemainingLengthResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }
}
