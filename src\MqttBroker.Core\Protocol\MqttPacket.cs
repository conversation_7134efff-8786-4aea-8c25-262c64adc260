using MqttBroker.Core.Models;

namespace MqttBroker.Core.Protocol;

/// <summary>
/// MQTT数据包基类
/// </summary>
public abstract class MqttPacket
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public abstract byte MessageType { get; }

    /// <summary>
    /// 数据包标识符（用于需要确认的消息）
    /// </summary>
    public int? PacketIdentifier { get; set; }

    /// <summary>
    /// 协议版本
    /// </summary>
    public MqttProtocolVersion ProtocolVersion { get; set; } = MqttProtocolVersion.V311;

    /// <summary>
    /// 序列化数据包为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public abstract byte[] Serialize();

    /// <summary>
    /// 从字节数组反序列化数据包
    /// </summary>
    /// <param name="buffer">字节数组</param>
    /// <param name="offset">起始偏移量</param>
    /// <param name="length">数据长度</param>
    /// <returns>是否成功反序列化</returns>
    public abstract bool Deserialize(byte[] buffer, int offset, int length);

    /// <summary>
    /// 验证数据包是否有效
    /// </summary>
    /// <returns>验证结果</returns>
    public abstract bool IsValid();

    /// <summary>
    /// 获取数据包大小
    /// </summary>
    /// <returns>数据包大小（字节）</returns>
    public abstract int GetPacketSize();
}

/// <summary>
/// CONNECT数据包
/// </summary>
public class ConnectPacket : MqttPacket
{
    public override byte MessageType => MqttProtocolConstants.MessageTypes.CONNECT;

    /// <summary>
    /// 协议名称
    /// </summary>
    public string ProtocolName { get; set; } = "MQTT";

    /// <summary>
    /// 协议级别
    /// </summary>
    public byte ProtocolLevel { get; set; } = MqttProtocolConstants.ProtocolVersions.V311;

    /// <summary>
    /// 连接标志
    /// </summary>
    public ConnectFlags ConnectFlags { get; set; } = new();

    /// <summary>
    /// 保持活跃时间（秒）
    /// </summary>
    public ushort KeepAlive { get; set; } = 60;

    /// <summary>
    /// 客户端标识符
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 遗嘱主题
    /// </summary>
    public string? WillTopic { get; set; }

    /// <summary>
    /// 遗嘱消息
    /// </summary>
    public byte[]? WillMessage { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public byte[]? Password { get; set; }

    /// <summary>
    /// MQTT 5.0 属性
    /// </summary>
    public Dictionary<byte, object>? Properties { get; set; }

    public override byte[] Serialize()
    {
        using var stream = new MemoryStream();
        using var writer = new BinaryWriter(stream);

        // 固定头部
        writer.Write((byte)((MessageType << 4) | 0x00)); // CONNECT消息类型

        // 可变头部和负载
        using var payloadStream = new MemoryStream();
        using var payloadWriter = new BinaryWriter(payloadStream);

        // 协议名称
        WriteString(payloadWriter, ProtocolName);

        // 协议级别
        payloadWriter.Write(ProtocolLevel);

        // 连接标志
        payloadWriter.Write(ConnectFlags.ToByte());

        // 保持活跃时间
        payloadWriter.Write((byte)(KeepAlive >> 8));
        payloadWriter.Write((byte)(KeepAlive & 0xFF));

        // MQTT 5.0 属性（如果适用）
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            WriteProperties(payloadWriter, Properties);
        }

        // 客户端标识符
        WriteString(payloadWriter, ClientId);

        // 遗嘱信息
        if (ConnectFlags.WillFlag)
        {
            if (ProtocolVersion == MqttProtocolVersion.V50)
            {
                WriteProperties(payloadWriter, null); // 遗嘱属性
            }
            WriteString(payloadWriter, WillTopic ?? string.Empty);
            WriteBytes(payloadWriter, WillMessage ?? Array.Empty<byte>());
        }

        // 用户名
        if (ConnectFlags.UsernameFlag)
        {
            WriteString(payloadWriter, Username ?? string.Empty);
        }

        // 密码
        if (ConnectFlags.PasswordFlag)
        {
            WriteBytes(payloadWriter, Password ?? Array.Empty<byte>());
        }

        var payload = payloadStream.ToArray();

        // 剩余长度
        WriteRemainingLength(writer, payload.Length);

        // 写入负载
        writer.Write(payload);

        return stream.ToArray();
    }

    public override bool Deserialize(byte[] buffer, int offset, int length)
    {
        try
        {
            var reader = new BinaryReader(new MemoryStream(buffer, offset, length));

            // 跳过固定头部（已在外部处理）
            var remainingLength = ReadRemainingLength(reader);

            // 协议名称
            ProtocolName = ReadString(reader);

            // 协议级别
            ProtocolLevel = reader.ReadByte();
            ProtocolVersion = ProtocolLevel == MqttProtocolConstants.ProtocolVersions.V50 
                ? MqttProtocolVersion.V50 
                : MqttProtocolVersion.V311;

            // 连接标志
            ConnectFlags = ConnectFlags.FromByte(reader.ReadByte());

            // 保持活跃时间
            KeepAlive = (ushort)((reader.ReadByte() << 8) | reader.ReadByte());

            // MQTT 5.0 属性
            if (ProtocolVersion == MqttProtocolVersion.V50)
            {
                Properties = ReadProperties(reader);
            }

            // 客户端标识符
            ClientId = ReadString(reader);

            // 遗嘱信息
            if (ConnectFlags.WillFlag)
            {
                if (ProtocolVersion == MqttProtocolVersion.V50)
                {
                    ReadProperties(reader); // 遗嘱属性
                }
                WillTopic = ReadString(reader);
                WillMessage = ReadBytes(reader);
            }

            // 用户名
            if (ConnectFlags.UsernameFlag)
            {
                Username = ReadString(reader);
            }

            // 密码
            if (ConnectFlags.PasswordFlag)
            {
                Password = ReadBytes(reader);
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    public override bool IsValid()
    {
        // 验证协议名称
        if (string.IsNullOrEmpty(ProtocolName) || ProtocolName != "MQTT")
            return false;

        // 验证协议级别
        if (ProtocolLevel != MqttProtocolConstants.ProtocolVersions.V311 && 
            ProtocolLevel != MqttProtocolConstants.ProtocolVersions.V50)
            return false;

        // 验证客户端ID
        if (string.IsNullOrEmpty(ClientId) && !ConnectFlags.CleanSession)
            return false;

        if (ClientId.Length > MqttProtocolConstants.Defaults.MaxClientIdLength)
            return false;

        // 验证遗嘱信息
        if (ConnectFlags.WillFlag)
        {
            if (string.IsNullOrEmpty(WillTopic) || WillMessage == null)
                return false;
        }

        // 验证用户名密码标志一致性
        if (ConnectFlags.PasswordFlag && !ConnectFlags.UsernameFlag)
            return false;

        return true;
    }

    public override int GetPacketSize()
    {
        var size = 1; // 固定头部消息类型

        // 协议名称长度
        size += 2 + (ProtocolName?.Length ?? 0);

        // 协议级别 + 连接标志 + 保持活跃时间
        size += 4;

        // MQTT 5.0 属性长度
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            size += GetPropertiesSize(Properties);
        }

        // 客户端ID长度
        size += 2 + (ClientId?.Length ?? 0);

        // 遗嘱信息长度
        if (ConnectFlags.WillFlag)
        {
            if (ProtocolVersion == MqttProtocolVersion.V50)
            {
                size += GetPropertiesSize(null); // 遗嘱属性
            }
            size += 2 + (WillTopic?.Length ?? 0);
            size += 2 + (WillMessage?.Length ?? 0);
        }

        // 用户名长度
        if (ConnectFlags.UsernameFlag)
        {
            size += 2 + (Username?.Length ?? 0);
        }

        // 密码长度
        if (ConnectFlags.PasswordFlag)
        {
            size += 2 + (Password?.Length ?? 0);
        }

        // 剩余长度字段大小
        size += GetRemainingLengthSize(size - 1);

        return size;
    }

    #region 辅助方法

    private static void WriteString(BinaryWriter writer, string value)
    {
        var bytes = System.Text.Encoding.UTF8.GetBytes(value);
        writer.Write((byte)(bytes.Length >> 8));
        writer.Write((byte)(bytes.Length & 0xFF));
        writer.Write(bytes);
    }

    private static void WriteBytes(BinaryWriter writer, byte[] value)
    {
        writer.Write((byte)(value.Length >> 8));
        writer.Write((byte)(value.Length & 0xFF));
        writer.Write(value);
    }

    private static string ReadString(BinaryReader reader)
    {
        var length = (reader.ReadByte() << 8) | reader.ReadByte();
        var bytes = reader.ReadBytes(length);
        return System.Text.Encoding.UTF8.GetString(bytes);
    }

    private static byte[] ReadBytes(BinaryReader reader)
    {
        var length = (reader.ReadByte() << 8) | reader.ReadByte();
        return reader.ReadBytes(length);
    }

    private static void WriteRemainingLength(BinaryWriter writer, int length)
    {
        do
        {
            var encodedByte = (byte)(length % 128);
            length /= 128;
            if (length > 0)
                encodedByte |= 128;
            writer.Write(encodedByte);
        } while (length > 0);
    }

    private static int ReadRemainingLength(BinaryReader reader)
    {
        var multiplier = 1;
        var value = 0;
        byte encodedByte;

        do
        {
            encodedByte = reader.ReadByte();
            value += (encodedByte & 127) * multiplier;
            multiplier *= 128;
        } while ((encodedByte & 128) != 0);

        return value;
    }

    private static int GetRemainingLengthSize(int length)
    {
        if (length < 128) return 1;
        if (length < 16384) return 2;
        if (length < 2097152) return 3;
        return 4;
    }

    private static void WriteProperties(BinaryWriter writer, Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
        {
            writer.Write((byte)0); // 属性长度为0
            return;
        }

        // TODO: 实现MQTT 5.0属性序列化
        writer.Write((byte)0);
    }

    private static Dictionary<byte, object>? ReadProperties(BinaryReader reader)
    {
        var length = reader.ReadByte();
        if (length == 0)
            return null;

        // TODO: 实现MQTT 5.0属性反序列化
        reader.ReadBytes(length);
        return new Dictionary<byte, object>();
    }

    private static int GetPropertiesSize(Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
            return 1; // 长度字节

        // TODO: 计算实际属性大小
        return 1;
    }

    #endregion
}

/// <summary>
/// 连接标志
/// </summary>
public class ConnectFlags
{
    /// <summary>
    /// 用户名标志
    /// </summary>
    public bool UsernameFlag { get; set; }

    /// <summary>
    /// 密码标志
    /// </summary>
    public bool PasswordFlag { get; set; }

    /// <summary>
    /// 遗嘱保留标志
    /// </summary>
    public bool WillRetain { get; set; }

    /// <summary>
    /// 遗嘱QoS等级
    /// </summary>
    public MqttQoSLevel WillQoS { get; set; }

    /// <summary>
    /// 遗嘱标志
    /// </summary>
    public bool WillFlag { get; set; }

    /// <summary>
    /// 清理会话标志
    /// </summary>
    public bool CleanSession { get; set; }

    /// <summary>
    /// 转换为字节
    /// </summary>
    /// <returns>标志字节</returns>
    public byte ToByte()
    {
        byte flags = 0;

        if (UsernameFlag) flags |= 0x80;
        if (PasswordFlag) flags |= 0x40;
        if (WillRetain) flags |= 0x20;
        flags |= (byte)((byte)WillQoS << 3);
        if (WillFlag) flags |= 0x04;
        if (CleanSession) flags |= 0x02;

        return flags;
    }

    /// <summary>
    /// 从字节创建标志
    /// </summary>
    /// <param name="flags">标志字节</param>
    /// <returns>连接标志对象</returns>
    public static ConnectFlags FromByte(byte flags)
    {
        return new ConnectFlags
        {
            UsernameFlag = (flags & 0x80) != 0,
            PasswordFlag = (flags & 0x40) != 0,
            WillRetain = (flags & 0x20) != 0,
            WillQoS = (MqttQoSLevel)((flags >> 3) & 0x03),
            WillFlag = (flags & 0x04) != 0,
            CleanSession = (flags & 0x02) != 0
        };
    }
}

/// <summary>
/// CONNACK数据包
/// </summary>
public class ConnackPacket : MqttPacket
{
    public override byte MessageType => MqttProtocolConstants.MessageTypes.CONNACK;

    /// <summary>
    /// 会话存在标志
    /// </summary>
    public bool SessionPresent { get; set; }

    /// <summary>
    /// 连接返回码
    /// </summary>
    public ConnectReturnCode ReturnCode { get; set; }

    /// <summary>
    /// MQTT 5.0 属性
    /// </summary>
    public Dictionary<byte, object>? Properties { get; set; }

    public override byte[] Serialize()
    {
        using var stream = new MemoryStream();
        using var writer = new BinaryWriter(stream);

        // 固定头部
        writer.Write((byte)((MessageType << 4) | 0x00));

        // 计算剩余长度
        var remainingLength = 2; // 连接确认标志 + 返回码
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            remainingLength += GetPropertiesSize(Properties);
        }

        WriteRemainingLength(writer, remainingLength);

        // 连接确认标志
        writer.Write((byte)(SessionPresent ? 0x01 : 0x00));

        // 返回码
        writer.Write((byte)ReturnCode);

        // MQTT 5.0 属性
        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            WriteProperties(writer, Properties);
        }

        return stream.ToArray();
    }

    public override bool Deserialize(byte[] buffer, int offset, int length)
    {
        try
        {
            var reader = new BinaryReader(new MemoryStream(buffer, offset, length));

            // 跳过固定头部
            var remainingLength = ReadRemainingLength(reader);

            // 连接确认标志
            var acknowledgeFlags = reader.ReadByte();
            SessionPresent = (acknowledgeFlags & 0x01) != 0;

            // 返回码
            ReturnCode = (ConnectReturnCode)reader.ReadByte();

            // MQTT 5.0 属性
            if (ProtocolVersion == MqttProtocolVersion.V50)
            {
                Properties = ReadProperties(reader);
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    public override bool IsValid()
    {
        return Enum.IsDefined(typeof(ConnectReturnCode), ReturnCode);
    }

    public override int GetPacketSize()
    {
        var size = 1; // 固定头部
        size += 2; // 连接确认标志 + 返回码

        if (ProtocolVersion == MqttProtocolVersion.V50)
        {
            size += GetPropertiesSize(Properties);
        }

        size += GetRemainingLengthSize(size - 1);
        return size;
    }

    #region 辅助方法 (复用ConnectPacket中的方法)

    private static void WriteRemainingLength(BinaryWriter writer, int length)
    {
        do
        {
            var encodedByte = (byte)(length % 128);
            length /= 128;
            if (length > 0)
                encodedByte |= 128;
            writer.Write(encodedByte);
        } while (length > 0);
    }

    private static int ReadRemainingLength(BinaryReader reader)
    {
        var multiplier = 1;
        var value = 0;
        byte encodedByte;

        do
        {
            encodedByte = reader.ReadByte();
            value += (encodedByte & 127) * multiplier;
            multiplier *= 128;
        } while ((encodedByte & 128) != 0);

        return value;
    }

    private static int GetRemainingLengthSize(int length)
    {
        if (length < 128) return 1;
        if (length < 16384) return 2;
        if (length < 2097152) return 3;
        return 4;
    }

    private static void WriteProperties(BinaryWriter writer, Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
        {
            writer.Write((byte)0);
            return;
        }
        // TODO: 实现MQTT 5.0属性序列化
        writer.Write((byte)0);
    }

    private static Dictionary<byte, object>? ReadProperties(BinaryReader reader)
    {
        var length = reader.ReadByte();
        if (length == 0)
            return null;
        // TODO: 实现MQTT 5.0属性反序列化
        reader.ReadBytes(length);
        return new Dictionary<byte, object>();
    }

    private static int GetPropertiesSize(Dictionary<byte, object>? properties)
    {
        if (properties == null || properties.Count == 0)
            return 1;
        // TODO: 计算实际属性大小
        return 1;
    }

    #endregion
}
